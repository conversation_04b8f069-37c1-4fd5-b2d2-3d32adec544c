# AI Assistant API Documentation

## Översikt

AI Assistant API:et tillhandahåller endpoints för att generera, optimera och felsöka RPA-flöden med hjälp av avancerade LLM-providers. API:et stöder flexibel provider-arkitektur med OpenAI och Azure OpenAI.

**Base URL**: `http://localhost:3002/api/ai-assistant`

## Autentisering

API:et använder server-side LLM provider-autentisering. Ingen client-side autentisering krävs för AI Assistant endpoints.

## LLM Provider-arkitektur

### Stödda Providers
- **OpenAI**: Direkt integration med OpenAI API
- **Azure OpenAI**: Enterprise-integration via Azure

### Provider-konfiguration
Providers konfigureras via miljövariabler på server-sidan:

```bash
LLM_PROVIDER=openai|azure
LLM_DEFAULT_MODEL=gpt-4o-mini
LLM_FALLBACK_MODEL=gpt-4o-mini
```

### Automatisk Fallback
API:et inkluderar automatisk fallback-hantering:
1. Försök med default model
2. Vid fel, försök med fallback model
3. Vid fortsatta fel, returnera strukturerat felmeddelande

## Endpoints

### 1. Health Check

**GET** `/health`

Kontrollerar AI Assistant status och aktiv LLM provider.

#### Response

```json
{
  "success": true,
  "data": {
    "aiStatus": "connected",
    "provider": "openai",
    "hasApiKey": true,
    "error": null,
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "message": "AI Assistant health check"
}
```

#### Status Codes
- `200 OK`: Health check genomförd (kontrollera `aiStatus` för faktisk status)

#### aiStatus Values
- `connected`: Provider fungerar korrekt
- `error`: Problem med provider eller konfiguration

---

### 2. Generate Flow

**POST** `/generate-flow`

Genererar ett komplett RPA-flöde från textbeskrivning.

#### Request Body

```json
{
  "prompt": "string (required)",
  "flowName": "string (optional)",
  "description": "string (optional)"
}
```

#### Example Request

```json
{
  "prompt": "Skapa ett flöde som loggar in på Gmail och söker efter olästa meddelanden",
  "flowName": "Gmail automation",
  "description": "Automatisk hantering av Gmail"
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "id": "flow_abc123",
    "name": "Gmail automation",
    "description": "Automatisk hantering av Gmail",
    "steps": [
      {
        "id": "step_1",
        "type": "navigate",
        "url": "https://gmail.com",
        "timeout": 30000,
        "description": "Navigera till Gmail"
      }
    ],
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Genererade 5 steg för flödet"
}
```

#### Status Codes
- `200 OK`: Flöde genererat framgångsrikt
- `400 Bad Request`: Ogiltig request data
- `500 Internal Server Error`: LLM provider-fel eller server-fel

---

### 3. Suggest Next Step

**POST** `/suggest-next-step`

Föreslår nästa steg för ett befintligt flöde.

#### Request Body

```json
{
  "flowId": "string (optional)",
  "currentSteps": "array (required)",
  "prompt": "string (required)"
}
```

#### Example Request

```json
{
  "flowId": "flow_123",
  "currentSteps": [
    {
      "id": "step_1",
      "type": "navigate",
      "url": "https://example.com"
    }
  ],
  "prompt": "Lägg till inloggningssteg med användarnamn och lösenord"
}
```

#### Response

```json
{
  "success": true,
  "data": [
    {
      "id": "step_2",
      "type": "waitForSelector",
      "selector": "#login-form",
      "timeout": 30000,
      "description": "Vänta på inloggningsformulär"
    },
    {
      "id": "step_3",
      "type": "fill",
      "selector": "#username",
      "value": "${username}",
      "description": "Fyll i användarnamn"
    }
  ],
  "message": "Föreslår 2 nya steg"
}
```

---

### 4. Optimize Flow

**POST** `/optimize-flow`

Optimerar ett befintligt flöde för specifika mål.

#### Request Body

```json
{
  "flowId": "string (required)",
  "currentSteps": "array (required)",
  "optimizationGoal": "string (optional, default: 'general')"
}
```

#### Optimization Goals
- `speed`: Optimera för snabbhet
- `reliability`: Optimera för tillförlitlighet
- `maintainability`: Optimera för underhållbarhet
- `general`: Allmän optimering

#### Example Request

```json
{
  "flowId": "flow_123",
  "currentSteps": [...],
  "optimizationGoal": "reliability"
}
```

#### Response

```json
{
  "success": true,
  "data": [
    {
      "id": "step_1",
      "type": "navigate",
      "url": "https://example.com",
      "timeout": 45000,
      "description": "Navigera med längre timeout för tillförlitlighet"
    }
  ],
  "message": "Optimerade flödet för reliability"
}
```

---

### 5. Debug Flow

**POST** `/debug-flow`

Analyserar problem i ett flöde och föreslår korrigeringar.

#### Request Body

```json
{
  "flowId": "string (required)",
  "currentSteps": "array (required)",
  "errorDescription": "string (required)",
  "executionLogs": "array (optional)"
}
```

#### Example Request

```json
{
  "flowId": "flow_123",
  "currentSteps": [...],
  "errorDescription": "Flödet fastnar på inloggningssidan",
  "executionLogs": [
    {
      "level": "error",
      "message": "Element not found: #login-button",
      "stepId": "step_2"
    }
  ]
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "steps": [...],
    "explanation": "Problemet verkar vara att login-knappen har en annan selektor. Jag har uppdaterat selektorn och lagt till en waitForSelector för att säkerställa att elementet laddas."
  },
  "message": "Analyserade problemet och föreslår 3 korrigerade steg"
}
```

## Error Handling

### Standard Error Response

```json
{
  "success": false,
  "error": "Error message",
  "details": "Additional error details (optional)"
}
```

### Common Error Codes

#### 400 Bad Request
- Ogiltig request body
- Saknade required fields
- Felaktigt format på data

#### 500 Internal Server Error
- LLM provider inte konfigurerad
- API-nyckel ogiltig eller saknas
- Rate limit överskriden
- Nätverksfel mot LLM provider

### LLM Provider-specifika Fel

#### OpenAI Provider
```json
{
  "success": false,
  "error": "LLM request failed with openai",
  "details": "Invalid API key"
}
```

#### Azure Provider
```json
{
  "success": false,
  "error": "LLM request failed with azure",
  "details": "Deployment not found: gpt-4o-mini-deployment"
}
```

## Rate Limiting

API:et följer LLM provider rate limits:

### OpenAI
- Tier-baserade limits
- Automatisk retry med exponential backoff
- Rate limit headers inkluderade i response

### Azure OpenAI
- Subscription-baserade limits
- Regional begränsningar
- Anpassningsbara limits via Azure Portal

## Best Practices

### Request Optimization
1. **Använd specifika prompts**: Tydliga beskrivningar ger bättre resultat
2. **Begränsa steg-antal**: Stora flöden kan delas upp i mindre delar
3. **Inkludera kontext**: Beskriv målwebbsida och användarscenario

### Error Handling
1. **Kontrollera health endpoint**: Innan kritiska operationer
2. **Implementera retry-logik**: För transient fel
3. **Hantera fallbacks**: Ha backup-strategier för AI-fel

### Performance
1. **Cache resultat**: För återkommande prompts
2. **Batch requests**: Kombinera relaterade operationer
3. **Övervaka response times**: Sätt upp monitoring

## SDK och Integration

### JavaScript/TypeScript

```typescript
import { aiService } from './services/ai'

// Generera flöde
const flow = await aiService.generateFlow({
  prompt: "Automatisera inloggning på webbsida",
  flowName: "Login automation"
})

// Kontrollera health
const health = await aiService.checkHealth()
if (health.aiStatus !== 'connected') {
  console.error('AI Assistant inte tillgänglig')
}
```

### cURL Examples

```bash
# Health check
curl http://localhost:3002/api/ai-assistant/health

# Generate flow
curl -X POST http://localhost:3002/api/ai-assistant/generate-flow \
     -H "Content-Type: application/json" \
     -d '{"prompt": "Navigera till Google och sök"}'

# Suggest next step
curl -X POST http://localhost:3002/api/ai-assistant/suggest-next-step \
     -H "Content-Type: application/json" \
     -d '{"currentSteps": [], "prompt": "Lägg till inloggning"}'
```

## Versioning

API:et följer semantic versioning:
- **Major**: Breaking changes i API-struktur
- **Minor**: Nya features, bakåtkompatibla
- **Patch**: Bugfixes och förbättringar

Aktuell version inkluderas i response headers:
```
X-API-Version: 1.0.0
X-LLM-Provider: openai
X-LLM-Model: gpt-4o-mini
```

## Support

### Debugging
1. Kontrollera health endpoint
2. Verifiera LLM provider-konfiguration
3. Granska server logs för detaljerade felmeddelanden

### Dokumentation
- [LLM Provider Configuration](../user-guide/LLM_PROVIDER_CONFIGURATION.md)
- [Troubleshooting Guide](../user-guide/LLM_TROUBLESHOOTING.md)
- [AI Assistant User Guide](../user-guide/AI_ASSISTANT_README.md)
