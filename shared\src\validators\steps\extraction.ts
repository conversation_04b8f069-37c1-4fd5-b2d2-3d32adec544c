import { RpaStep, ExtractTextStep, ExtractAttributeStep } from '../../types/steps';
import { ValidationResult, ValidationError, createStepFromType } from '../../utils';

export function validateExtractionStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'extractText':
    case 'extractAttribute':
      // Both require selector and variableName
      if (!step.selector || step.selector.trim() === '') {
        errors.push({
          field: 'selector',
          message: 'Selector is required',
          
        });
      }

      // variableName is now optional - if not provided, a default will be used

      // Additional validation for extractAttribute
      if (step.type === 'extractAttribute') {
        const attrStep = step as ExtractAttributeStep;
        if (!attrStep.attribute || attrStep.attribute.trim() === '') {
          errors.push({
            field: 'attribute',
            message: 'Attribute name is required',
            
          });
        }
      }
      break;

    case 'takeScreenshot':
      // Screenshot step doesn't require additional validation
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function createExtractionStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'extractText':
    case 'extractAttribute':
    case 'takeScreenshot':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not an extraction step type: ${stepType}`);
  }
}
