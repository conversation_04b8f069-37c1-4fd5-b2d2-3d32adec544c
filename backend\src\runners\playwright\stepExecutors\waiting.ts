import { Page } from 'playwright';
import { RpaStep, ExecutionLog } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';

/**
 * Waiting step executors for PlaywrightRunner
 */

export interface WaitingExecutorContext {
  page: Page;
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
}

/**
 * Execute waitForSelector step
 */
export async function executeWaitForSelector(
  step: RpaStep & { selector: string; state?: 'attached' | 'detached' | 'visible' | 'hidden' },
  context: WaitingExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedWaitSelector = interpolateVariables(step.selector, variables);
  await page.waitForSelector(interpolatedWaitSelector, {
    state: step.state || 'visible',
    timeout
  });

  onLog({
    level: 'info',
    message: `Waited for selector: ${interpolatedWaitSelector}`,
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute waitForTimeout step
 */
export async function executeWaitForTimeout(
  step: RpaStep & { duration: number },
  context: WaitingExecutorContext
): Promise<StepExecutionResult> {
  const { page, onLog } = context;

  await page.waitForTimeout(step.duration);
  onLog({
    level: 'info',
    message: `Waited for ${step.duration}ms`,
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute waitForUrl step
 */
export async function executeWaitForUrl(
  step: RpaStep & { url: string | RegExp },
  context: WaitingExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedWaitUrl = interpolateVariables(step.url.toString(), variables);
  await page.waitForURL(interpolatedWaitUrl, { timeout });
  onLog({
    level: 'info',
    message: `Waited for URL: ${interpolatedWaitUrl}`,
    stepId: step.id
  });

  return { success: true };
}
