import { RpaStepBase } from './base';

// Wait steps
export interface WaitForSelectorStep extends RpaStepBase {
  type: 'waitForSelector';
  selector: string;
  state?: 'attached' | 'detached' | 'visible' | 'hidden';
}

export interface WaitForTimeoutStep extends RpaStepBase {
  type: 'waitForTimeout';
  duration: number; // milliseconds
}

export interface WaitForUrlStep extends RpaStepBase {
  type: 'waitForUrl';
  url: string | RegExp;
}
