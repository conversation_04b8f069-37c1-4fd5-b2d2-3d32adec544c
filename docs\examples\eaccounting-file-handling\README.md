# eAccounting Filhantering - Exempel och Best Practices

Detta dokument beskriver hur man använder eAccounting filhanteringsfunktioner i RPA-flöden för att ladda upp filer och koppla dem till verifikationer.

## Översikt

eAccounting filhantering består av fyra huvudsakliga steg:

1. **`eAccountingUploadFile`** - Ladda upp fil till eAccounting Attachments
2. **`eAccountingAttachFileToVoucher`** - Koppla fil till befintlig verifikation
3. **`eAccountingCreateVoucher`** - Skapa verifikation med AI-assistans
4. **`eAccountingUploadAndCreateVoucher`** - Kombinerat steg för fil + verifikation

## Förutsättningar

- eAccounting OAuth2-token konfigurerat för kunden
- Filer måste vara i base64-format (från `downloadFile` eller liknande steg)
- <PERSON><PERSON>ga eAccounting API-behörigheter för attachments och vouchers
- API-scopes: `ea:accounting`, `ea:purchase`

## Grundläggande Arbetsflöden

### 1. Enkel Filuppladdning

```yaml
# Steg 1: Ladda ner fil från webbsida
- type: downloadFile
  name: "Ladda ner faktura"
  triggerSelector: ".download-invoice"
  variableName: "invoice-file"
  saveToFile: false

# Steg 2: Ladda upp till eAccounting
- type: eAccountingUploadFile
  name: "Ladda upp faktura till eAccounting"
  inputVariable: "invoice-file"
  filename: "faktura-${invoice-number}.pdf"
  contentType: "application/pdf"
  comment: "Inköpsfaktura från leverantör"
  variableName: "eaccounting-file"
```

### 2. Skapa Verifikation med Filkoppling

```yaml
# Steg 1: Ladda upp fil
- type: eAccountingUploadFile
  name: "Ladda upp kvitto"
  inputVariable: "receipt-file"
  variableName: "uploaded-receipt"

# Steg 2: Skapa verifikation med AI
- type: eAccountingCreateVoucher
  name: "Skapa verifikation"
  inputVariable: "receipt-data"
  aiPrompt: "Skapa en utgiftsverifikation för detta kvitto. Använd konto 6250 för kostnader och 2440 för leverantörsskuld."
  voucherText: "Utgift enligt kvitto"
  variableName: "voucher-result"

# Steg 3: Koppla fil till verifikation
- type: eAccountingAttachFileToVoucher
  name: "Koppla fil till verifikation"
  attachmentIdVariable: "uploaded-receipt"
  voucherIdVariable: "voucher-result"
  variableName: "attachment-result"
```

### 3. Kombinerad Operation (Rekommenderat)

```yaml
# Ett steg som gör allt
- type: eAccountingUploadAndCreateVoucher
  name: "Ladda upp och skapa verifikation"
  fileInputVariable: "invoice-file"
  filename: "faktura-${invoice-number}.pdf"
  contentType: "application/pdf"
  fileComment: "Inköpsfaktura"
  voucherInputVariable: "invoice-data"
  aiPrompt: "Skapa en inköpsverifikation med moms. Använd konto 4010 för inköp, 2640 för ingående moms och 2440 för leverantörsskuld."
  voucherText: "Inköpsfaktura ${invoice-number}"
  voucherDate: "2024-01-15"
  variableName: "complete-result"
```

## Variabelhantering

### Input-variabler

Filstegen förväntar sig base64-kodade filer från tidigare steg:

```yaml
# Från downloadFile
variables:
  my-file: "base64-content..."
  my-file_filename: "document.pdf"
  my-file_filepath: "/path/to/file"

# Från eAccountingUploadFile
variables:
  eaccounting-file:
    attachmentId: "abc123-def456"
    filename: "document.pdf"
    contentType: "application/pdf"
    comment: "Uploaded document"
    temporaryUrl: "https://..."
```

### Output-variabler

```yaml
# eAccountingUploadFile resultat
{
  "attachmentId": "abc123-def456",
  "filename": "document.pdf",
  "contentType": "application/pdf",
  "comment": "Uploaded document",
  "temporaryUrl": "https://...",
  "uploadedBy": "<EMAIL>",
  "imageDate": "2024-01-15T10:30:00Z",
  "attachmentStatus": 1
}

# eAccountingCreateVoucher resultat
{
  "voucherId": "voucher-123",
  "voucherNumber": "A001",
  "voucherSeries": "A",
  "voucherDate": "2024-01-15",
  "voucherText": "Inköpsfaktura",
  "totalAmount": 1250.00,
  "rowsCount": 3,
  "aiExplanation": "Inköpsverifikation skapad...",
  "createdUtc": "2024-01-15T10:30:00Z"
}

# eAccountingUploadAndCreateVoucher resultat
{
  "attachmentId": "abc123-def456",
  "filename": "document.pdf",
  "contentType": "application/pdf",
  "voucherId": "voucher-123",
  "voucherNumber": "A001",
  "voucherDate": "2024-01-15",
  "voucherText": "Inköpsfaktura",
  "totalAmount": 1250.00,
  "rowsCount": 3,
  "aiExplanation": "Kombinerad operation...",
  "documentId": "voucher-123",
  "documentType": 3,
  "success": true,
  "message": "Successfully uploaded file, created voucher, and attached file..."
}
```

## Best Practices

### 1. Filnamnshantering

```yaml
# Använd dynamiska filnamn
filename: "faktura-${supplier-name}-${date}.pdf"

# Eller låt systemet använda originalnamnet
# filename: "" # Använder {inputVariable}_filename
```

### 2. Content Type Hantering

```yaml
# Specificera content type för bästa resultat
contentType: "application/pdf"  # För PDF-filer
contentType: "image/jpeg"       # För JPEG-bilder
contentType: "image/png"        # För PNG-bilder
contentType: "image/tiff"       # För TIFF-bilder

# Eller låt systemet auto-detektera från filnamn
# contentType: "" # Auto-detekterar från filnamn
```

### 3. Felhantering

```yaml
# Använd beskrivande namn för felsökning
- type: eAccountingUploadFile
  name: "Ladda upp faktura från ${supplier-name}"
  inputVariable: "invoice-file"
  comment: "Faktura ${invoice-number} från ${supplier-name}"
```

### 4. AI-prompter för Verifikationer

```yaml
# Specifika prompter ger bättre resultat
aiPrompt: |
  Skapa en inköpsverifikation för denna faktura:
  - Använd konto 4010 för varor/tjänster
  - Använd konto 2640 för ingående moms (25%)
  - Använd konto 2440 för leverantörsskuld
  - Kontrollera att verifikationen är balanserad
  - Inkludera leverantörsnamn i beskrivningen
  - Alla belopp ska ha max 2 decimaler
```

### 5. Datumhantering

```yaml
# Använd ISO-format för datum
voucherDate: "2024-01-15"  # YYYY-MM-DD

# Eller låt systemet använda dagens datum
# voucherDate: "" # Använder dagens datum
```

## Komplett Exempel: Fakturahantering

```yaml
flow:
  name: "Automatisk Fakturahantering med eAccounting"
  steps:
    # 1. Navigera till leverantörsportal
    - type: navigate
      name: "Gå till leverantörsportal"
      url: "https://portal.supplier.com/invoices"

    # 2. Logga in
    - type: fillPassword
      name: "Logga in"
      usernameSelector: "#username"
      passwordSelector: "#password"
      credentialId: "supplier-login"

    # 3. Extrahera fakturadata
    - type: extractText
      name: "Hämta fakturanummer"
      selector: ".invoice-number"
      variableName: "invoice-number"

    - type: extractText
      name: "Hämta leverantörsnamn"
      selector: ".supplier-name"
      variableName: "supplier-name"

    - type: extractText
      name: "Hämta totalbelopp"
      selector: ".total-amount"
      variableName: "total-amount"

    # 4. Ladda ner faktura-PDF
    - type: downloadFile
      name: "Ladda ner faktura"
      triggerSelector: ".download-pdf"
      variableName: "invoice-pdf"
      saveToFile: false

    # 5. Kombinerad uppladdning och verifikationsskapande
    - type: eAccountingUploadAndCreateVoucher
      name: "Skapa verifikation med bifogad faktura"
      fileInputVariable: "invoice-pdf"
      filename: "faktura-${supplier-name}-${invoice-number}.pdf"
      contentType: "application/pdf"
      fileComment: "Faktura ${invoice-number} från ${supplier-name}"
      voucherInputVariable: "total-amount"
      aiPrompt: |
        Skapa en inköpsverifikation för faktura ${invoice-number} från ${supplier-name}:
        - Totalbelopp: ${total-amount} SEK (inklusive 25% moms)
        - Använd konto 4010 för inköp av varor/tjänster
        - Använd konto 2640 för ingående moms
        - Använd konto 2440 för leverantörsskuld
        - Sätt leverantörsnamn i beskrivningen
        - Alla belopp ska ha max 2 decimaler
      voucherText: "Inköpsfaktura ${invoice-number} - ${supplier-name}"
      voucherDate: "2024-01-15"
      variableName: "voucher-with-file"

    # 6. Logga resultat
    - type: takeScreenshot
      name: "Ta skärmdump av resultat"
      variableName: "completion-screenshot"
```

## Felsökning

### Vanliga Problem

1. **"No file content found in variable"**
   - Kontrollera att `downloadFile` lyckades
   - Verifiera variabelnamnet

2. **"Invalid base64 data"**
   - Filen kanske inte laddades ner korrekt
   - Kontrollera att `saveToFile: false` är satt

3. **"eAccounting API error 400"**
   - Kontrollera OAuth2-token och scopes
   - Verifiera filformat (endast PDF, JPEG, PNG, TIFF stöds)
   - Kontrollera filnamn (max 200 tecken, endast alfanumeriska tecken och - _ () )

4. **"Voucher is not balanced"**
   - AI-prompten behöver vara mer specifik
   - Kontrollera input-data kvalitet
   - Säkerställ att alla belopp har max 2 decimaler

5. **"Invalid account number format"**
   - Kontonummer måste vara numeriska
   - Kontrollera att AI-prompten specificerar giltiga konton

### Debug-tips

```yaml
# Lägg till logging för felsökning
- type: takeScreenshot
  name: "Debug: Före filuppladdning"
  variableName: "debug-before"

# Använd beskrivande variabelnamn
variableName: "invoice-file-from-supplier-portal"
```

## API-referens

Se [eAccounting API-dokumentation](https://eaccountingapi.vismaonline.com/swagger) för detaljer om:
- Attachments API (`/v2/attachments`)
- Attachment Links API (`/v2/attachmentlinks`)
- Vouchers API (`/v2/vouchers`)

## Skillnader från Fortnox

- **Content Type**: eAccounting kräver explicit content type eller auto-detekterar från filnamn
- **Filformat**: Stöder PDF, JPEG, PNG, TIFF (inte alla format som Fortnox)
- **Attachment ID**: Använder GUID-format istället för numeriska ID:n
- **Voucher Structure**: Olika fältnamn (DebitAmount/CreditAmount vs Debit/Credit)
- **Date Format**: Kräver ISO-format (YYYY-MM-DD)
- **Account Numbers**: Måste vara numeriska, inte alfanumeriska som Fortnox
