# Guide: <PERSON><PERSON><PERSON><PERSON> till Nya Runners

Denna guide visar hur du lägger till en ny runner i RPA-systemet. En runner är ansvarig för att utföra specifika typer av RPA-steg. Processen tar cirka 2 timmar.

## Översikt

För att lägga till en ny runner behöver du:

1. **Skapa runner-klass** som implementerar IRunner interface
2. **Implementera step-executors** för steg-typer som runner hanterar
3. **Registrera runner** i factory och registry
4. **Skapa UI-komponenter** för runner-specifika inställningar
5. **Lägga till tester** för att säkerställa kvalitet

## När Skapa Ny Runner?

Skapa en ny runner när:

- Du behöver integrera med en ny tjänst/API
- Du vill hantera en ny typ av automation (t.ex. desktop automation)
- Du behöver specialiserad logik som inte passar i befintliga runners
- <PERSON> vill isolera komplex funktionalitet

Exempel på runners:
- **PlaywrightRunner**: Web browser automation
- **AIRunner**: AI/LLM processing
- **APIRunner**: REST/GraphQL API calls
- **DatabaseRunner**: Direct database operations
- **DesktopRunner**: Desktop application automation

## Steg-för-steg Guide

### 1. Skapa Runner-klass

Skapa ny mapp och runner-klass:

```bash
mkdir backend/src/runners/my-new-runner
```

```typescript
// backend/src/runners/my-new-runner/MyNewRunner.ts
import { BaseRunner } from '../base/BaseRunner';
import { RpaStep } from '../../../shared/src/types/steps';
import { ExecutionContext } from '../types';

export class MyNewRunner extends BaseRunner {
  public readonly runnerType = 'myNewRunner' as const;
  
  // Runner-specifika properties
  private client?: SomeApiClient;
  private config: RunnerConfig;

  constructor(config?: RunnerConfig) {
    super();
    this.config = config || {};
    this.initializeRunner();
  }

  private async initializeRunner(): Promise<void> {
    try {
      // Initialisera runner-specifika resurser
      this.client = new SomeApiClient({
        apiKey: process.env.MY_API_KEY,
        baseUrl: process.env.MY_API_BASE_URL
      });
      
      this.logInfo('MyNewRunner initialiserad');
    } catch (error) {
      this.logError(`Fel vid initialisering: ${error}`);
      throw error;
    }
  }

  public canExecuteStep(step: RpaStep): boolean {
    const supportedSteps = [
      'myCustomStep',
      'anotherCustomStep',
    ];
    
    return supportedSteps.includes(step.type);
  }

  public async executeStep(step: RpaStep, context: ExecutionContext): Promise<void> {
    if (!this.canExecuteStep(step)) {
      throw new Error(`MyNewRunner kan inte hantera steg: ${step.type}`);
    }

    this.logInfo(`Utför ${step.type}: ${step.name}`);

    try {
      switch (step.type) {
        case 'myCustomStep':
          await this.executeMyCustomStep(step as MyCustomStep, context);
          break;
        case 'anotherCustomStep':
          await this.executeAnotherCustomStep(step as AnotherCustomStep, context);
          break;
        default:
          throw new Error(`Ostödd steg-typ: ${step.type}`);
      }

      this.logInfo(`${step.type} slutfört framgångsrikt`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Okänt fel';
      this.logError(`Fel vid ${step.type}: ${errorMessage}`);
      throw error;
    }
  }

  public async start(): Promise<void> {
    try {
      this.logInfo('Startar MyNewRunner');
      
      // Utför start-logik
      await this.client?.authenticate();
      
      this.logInfo('MyNewRunner startad');
    } catch (error) {
      this.logError(`Fel vid start: ${error}`);
      throw error;
    }
  }

  public async stop(): Promise<void> {
    try {
      this.logInfo('Stoppar MyNewRunner');
      
      // Cleanup
      await this.client?.disconnect();
      
      this.logInfo('MyNewRunner stoppad');
    } catch (error) {
      this.logError(`Fel vid stopp: ${error}`);
    }
  }

  public getStatus(): { isReady: boolean; details: string } {
    const isConnected = this.client?.isConnected() || false;
    return {
      isReady: isConnected,
      details: isConnected ? 'API ansluten' : 'API inte ansluten'
    };
  }

  // Step-specifika metoder
  private async executeMyCustomStep(
    step: MyCustomStep, 
    context: ExecutionContext
  ): Promise<void> {
    // Implementera step-logik
    const result = await this.client?.performAction(step.actionData);
    context.variables[`var-${step.type}-result`] = result;
  }

  private async executeAnotherCustomStep(
    step: AnotherCustomStep, 
    context: ExecutionContext
  ): Promise<void> {
    // Implementera annan step-logik
  }
}
```

### 2. Skapa Step Executors

Organisera step-logik i separata executors:

```typescript
// backend/src/runners/my-new-runner/stepExecutors/index.ts
import { ExecutionContext } from '../../types';

export abstract class BaseStepExecutor {
  protected logInfo(message: string): void {
    console.log(`[${new Date().toISOString()}] INFO: ${message}`);
  }

  protected logError(message: string): void {
    console.error(`[${new Date().toISOString()}] ERROR: ${message}`);
  }
}

export class MyCustomStepExecutor extends BaseStepExecutor {
  private client: SomeApiClient;

  constructor(client: SomeApiClient) {
    super();
    this.client = client;
  }

  async execute(step: MyCustomStep, context: ExecutionContext): Promise<void> {
    try {
      this.logInfo(`Utför myCustomStep: ${step.name}`);

      // Validera step-data
      this.validateStep(step);

      // Utför huvudåtgärd
      const result = await this.performAction(step, context);

      // Uppdatera context
      this.updateContext(step, result, context);

      this.logInfo('myCustomStep slutfört');

    } catch (error) {
      this.logError(`Fel vid myCustomStep: ${error}`);
      throw error;
    }
  }

  private validateStep(step: MyCustomStep): void {
    if (!step.requiredField) {
      throw new Error('Obligatoriskt fält saknas');
    }
  }

  private async performAction(
    step: MyCustomStep, 
    context: ExecutionContext
  ): Promise<any> {
    // Implementera huvudlogik
    return await this.client.makeRequest({
      endpoint: step.endpoint,
      data: step.data,
      method: step.method
    });
  }

  private updateContext(
    step: MyCustomStep, 
    result: any, 
    context: ExecutionContext
  ): void {
    context.variables[`var-${step.type}-result`] = result;
    context.variables[`var-${step.type}-timestamp`] = new Date().toISOString();
  }
}

export class StepExecutorFactory {
  private myCustomExecutor: MyCustomStepExecutor;

  constructor(client: SomeApiClient) {
    this.myCustomExecutor = new MyCustomStepExecutor(client);
  }

  getExecutor(stepType: string): BaseStepExecutor {
    switch (stepType) {
      case 'myCustomStep':
        return this.myCustomExecutor;
      default:
        throw new Error(`Ingen executor för: ${stepType}`);
    }
  }

  supportsStepType(stepType: string): boolean {
    return ['myCustomStep'].includes(stepType);
  }
}
```

### 3. Registrera Runner

Uppdatera runner registry:

```typescript
// backend/src/runners/registry/stepTypes.ts
export const STEP_RUNNER_MAPPING = {
  // Befintliga mappningar...
  
  // Nya steg för din runner
  myCustomStep: 'myNewRunner',
  anotherCustomStep: 'myNewRunner',
} as const;

export type RunnerType = 
  | 'playwright' 
  | 'ai' 
  | 'myNewRunner'  // Lägg till här
  | 'api';
```

Uppdatera runner factory:

```typescript
// backend/src/runners/factory/RunnerFactory.ts
import { MyNewRunner } from '../my-new-runner/MyNewRunner';

export class RunnerFactory {
  createRunner(type: RunnerType): IRunner {
    switch (type) {
      case 'playwright':
        return new PlaywrightRunner();
      case 'ai':
        return new AIRunner();
      case 'myNewRunner':
        return new MyNewRunner();
      default:
        throw new Error(`Unknown runner type: ${type}`);
    }
  }
}
```

### 4. Skapa UI-komponenter

Om runner behöver specifika inställningar, skapa UI-komponenter:

```typescript
// frontend/src/components/settings/runners/MyNewRunnerSettings.tsx
import React, { useState } from 'react';

interface MyNewRunnerSettingsProps {
  settings: MyNewRunnerSettings;
  onSettingsChange: (settings: MyNewRunnerSettings) => void;
}

export const MyNewRunnerSettings: React.FC<MyNewRunnerSettingsProps> = ({
  settings,
  onSettingsChange
}) => {
  const handleChange = (field: string, value: any) => {
    onSettingsChange({
      ...settings,
      [field]: value
    });
  };

  return (
    <div className="runner-settings">
      <h3>MyNewRunner Inställningar</h3>
      
      <div className="form-group">
        <label>API Nyckel</label>
        <input
          type="password"
          value={settings.apiKey || ''}
          onChange={(e) => handleChange('apiKey', e.target.value)}
          placeholder="Din API nyckel"
          className="form-input"
        />
      </div>

      <div className="form-group">
        <label>Base URL</label>
        <input
          type="url"
          value={settings.baseUrl || ''}
          onChange={(e) => handleChange('baseUrl', e.target.value)}
          placeholder="https://api.example.com"
          className="form-input"
        />
      </div>

      <div className="form-group">
        <label>Timeout (ms)</label>
        <input
          type="number"
          value={settings.timeout || 30000}
          onChange={(e) => handleChange('timeout', parseInt(e.target.value))}
          min="1000"
          max="300000"
          className="form-input"
        />
      </div>
    </div>
  );
};
```

### 5. Skapa Tester

Skapa omfattande tester för din runner:

```typescript
// backend/src/runners/my-new-runner/__tests__/MyNewRunner.test.ts
import { MyNewRunner } from '../MyNewRunner';
import { ExecutionContext } from '../../types';

describe('MyNewRunner', () => {
  let runner: MyNewRunner;
  let mockContext: ExecutionContext;

  beforeEach(() => {
    runner = new MyNewRunner();
    mockContext = {
      variables: {},
      flowId: 'test-flow',
      executionId: 'test-execution',
      customerId: 'test-customer'
    };
  });

  afterEach(async () => {
    await runner.stop();
  });

  describe('Initialisering', () => {
    test('ska skapa runner-instans', () => {
      expect(runner).toBeInstanceOf(MyNewRunner);
      expect(runner.runnerType).toBe('myNewRunner');
    });

    test('ska ha korrekt status', () => {
      const status = runner.getStatus();
      expect(status.isReady).toBeDefined();
      expect(status.details).toBeDefined();
    });
  });

  describe('Step-hantering', () => {
    test('ska identifiera stödda steg', () => {
      const supportedStep = {
        id: 'test-1',
        type: 'myCustomStep',
        name: 'Test Step'
      };

      expect(runner.canExecuteStep(supportedStep as any)).toBe(true);
    });

    test('ska utföra giltigt steg', async () => {
      const step = {
        id: 'test-1',
        type: 'myCustomStep',
        name: 'Test Step',
        requiredField: 'test-value'
      };

      await runner.start();

      await expect(
        runner.executeStep(step as any, mockContext)
      ).resolves.not.toThrow();
    });
  });

  describe('Livscykel', () => {
    test('ska starta och stoppa korrekt', async () => {
      await expect(runner.start()).resolves.not.toThrow();
      await expect(runner.stop()).resolves.not.toThrow();
    });
  });
});
```

### 6. Dokumentation

Skapa README för din runner:

```markdown
# MyNewRunner

Beskrivning av vad runner gör och när den ska användas.

## Stödda Steg-typer

- `myCustomStep` - Beskrivning av steget
- `anotherCustomStep` - Beskrivning av annat steg

## Konfiguration

### Miljövariabler

```env
MY_API_KEY=your_api_key
MY_API_BASE_URL=https://api.example.com
MY_API_TIMEOUT=30000
```

### Användning

```typescript
const runner = new MyNewRunner({
  apiKey: 'your-key',
  baseUrl: 'https://api.example.com'
});

await runner.start();
await runner.executeStep(step, context);
await runner.stop();
```

## API Referens

[Detaljerad API-dokumentation]
```

## Vanliga Misstag

### 1. Glömma registrera runner

Se till att runner är registrerad i både factory och registry.

### 2. Felaktig step-mappning

Kontrollera att steg-typer mappas till rätt runner.

### 3. Saknad felhantering

Implementera robust felhantering i alla metoder.

### 4. Resursläckage

Se till att alla resurser rensas upp i stop()-metoden.

### 5. Bristande validering

Validera all input innan processing.

## Best Practices

### 1. Separation of Concerns

- Håll runner-logik separerad från step-logik
- Använd step-executors för komplex logik
- Isolera externa dependencies

### 2. Felhantering

- Implementera retry-logik för tillfälliga fel
- Logga alla fel med kontext
- Ge användarvänliga felmeddelanden

### 3. Prestanda

- Använd connection pooling
- Implementera caching när lämpligt
- Optimera för parallell exekvering

### 4. Säkerhet

- Validera all input
- Använd säker autentisering
- Logga säkerhetsrelevanta händelser

### 5. Testbarhet

- Skriv enhetstester för alla metoder
- Mocka externa dependencies
- Testa felscenarier

## Felsökning

### Runner startar inte

1. Kontrollera miljövariabler
2. Verifiera API-anslutning
3. Kontrollera loggar

### Steg misslyckas

1. Validera step-data
2. Kontrollera API-status
3. Verifiera permissions

### Prestanda-problem

1. Kontrollera nätverkslatens
2. Optimera API-anrop
3. Implementera caching

## Exempel

### Grundläggande API-anrop

```typescript
const step: MyCustomStep = {
  id: 'api-call-1',
  type: 'myCustomStep',
  name: 'Hämta användardata',
  endpoint: '/users/123',
  method: 'GET'
};
```

### Komplex databehandling

```typescript
const step: AnotherCustomStep = {
  id: 'process-1',
  type: 'anotherCustomStep',
  name: 'Bearbeta data',
  inputData: '${var-previous-result}',
  transformation: 'json-to-csv'
};
```

## Se Också

- [Lägga till Nya Steg](./adding-new-steps.md)
- [Arkitektur Översikt](./architecture.md)
- [Kodkonventioner](./conventions.md)
- [Felsökning](./troubleshooting.md)
