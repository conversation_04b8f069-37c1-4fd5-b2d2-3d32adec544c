# Production Docker Compose Configuration
# Använd: docker-compose -f docker-compose.prod.yml up -d

services:
  redis:
    image: redis:7-alpine
    container_name: rpa-redis-prod
    ports:
      - "127.0.0.1:6379:6379"  # Bind endast till localhost
    volumes:
      - redis_data_prod:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: always
    networks:
      - rpa-network-prod
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: rpa-backend-prod
    ports:
      - "127.0.0.1:3002:3002"  # Bind endast till localhost
    env_file:
      - .env.docker
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - PORT=3002
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./screenshots:/app/screenshots
      - ./downloads:/app/downloads
      - ./backend/data:/app/data
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 60s
      timeout: 15s
      retries: 3
      start_period: 60s
    restart: always
    networks:
      - rpa-network-prod
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    container_name: rpa-frontend-prod
    ports:
      - "80:80"      # HTTP
      - "443:443"    # HTTPS (om du konfigurerar SSL)
    depends_on:
      backend:
        condition: service_healthy
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 60s
      timeout: 15s
      retries: 3
      start_period: 30s
    restart: always
    networks:
      - rpa-network-prod
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

volumes:
  redis_data_prod:
    driver: local

networks:
  rpa-network-prod:
    driver: bridge
