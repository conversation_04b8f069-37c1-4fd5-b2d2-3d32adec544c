export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  maxTokens?: number;
}

export interface ChatCompletionResponse {
  content: string;
  usage?: TokenUsage;
}

export interface LLMProvider {
  name: string;
  isConfigured(): boolean;
  getSupportedModels(): string[];
  createChatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
  testConnection(): Promise<boolean>;
}
