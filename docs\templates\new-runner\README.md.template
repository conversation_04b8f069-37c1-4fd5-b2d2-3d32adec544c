# {{PASCAL_CASE}}Runner

{{STEP_DESCRIPTION}} runner för RPA-systemet.

## Översikt

{{PASCAL_CASE}}Runner hanterar steg som kräver {{STEP_DESCRIPTION}} funktionalitet. Denna runner är designad för att vara skalbar och lätt att underhålla.

## Stödda Steg-typer

- `{{STEP_TYPE}}` - {{STEP_DESCRIPTION}}
<!-- <PERSON><PERSON><PERSON> till fler steg-typer som runner hanterar -->

## Installation och Konfiguration

### Miljövariabler

Lägg till följande miljövariabler i `.env` filen:

```env
# {{PASCAL_CASE}}Runner konfiguration
{{UPPER_CASE}}_API_KEY=your_api_key_here
{{UPPER_CASE}}_BASE_URL=https://api.example.com
{{UPPER_CASE}}_TIMEOUT=30000
```

### Dependencies

Installera nödvändiga dependencies:

```bash
npm install package-name-here
```

## Användning

### Grundläggande Användning

```typescript
import { {{PASCAL_CASE}}Runner } from './{{PASCAL_CASE}}Runner';

const runner = new {{PASCAL_CASE}}Runner();

// Starta runner
await runner.start();

// Utför steg
const step = {
  id: 'step-1',
  type: '{{STEP_TYPE}}',
  name: 'Mitt {{STEP_TYPE}} steg',
  // Lägg till step-specifika properties
};

const context = {
  variables: {},
  flowId: 'flow-1',
  executionId: 'exec-1',
  customerId: 'customer-1'
};

await runner.executeStep(step, context);

// Stoppa runner
await runner.stop();
```

### Avancerad Konfiguration

```typescript
const runner = new {{PASCAL_CASE}}Runner({
  // Lägg till konfigurationsalternativ
  timeout: 30000,
  retries: 3,
  // ...
});
```

## API Referens

### {{PASCAL_CASE}}Runner

#### Konstruktor

```typescript
constructor(config?: RunnerConfig)
```

#### Metoder

##### `canExecuteStep(step: RpaStep): boolean`

Kontrollerar om runner kan hantera ett specifikt steg.

##### `executeStep(step: RpaStep, context: ExecutionContext): Promise<void>`

Utför ett RPA-steg.

##### `start(): Promise<void>`

Startar runner och initialiserar resurser.

##### `stop(): Promise<void>`

Stoppar runner och rensar upp resurser.

##### `getStatus(): { isReady: boolean; details: string }`

Hämtar aktuell status för runner.

## Steg-typer

### {{STEP_TYPE}}

{{STEP_DESCRIPTION}}

#### Properties

| Property | Typ | Obligatorisk | Beskrivning |
|----------|-----|--------------|-------------|
| `type` | `'{{STEP_TYPE}}'` | Ja | Steg-typ |
| `name` | `string` | Ja | Namn på steget |
| `description` | `string` | Nej | Beskrivning av steget |
<!-- Lägg till step-specifika properties -->

#### Exempel

```typescript
const step: {{PASCAL_CASE}}Step = {
  id: 'step-1',
  type: '{{STEP_TYPE}}',
  name: 'Mitt {{STEP_TYPE}} steg',
  description: 'Beskrivning av vad steget gör',
  // Lägg till step-specifika properties
};
```

#### Skapade Variabler

Detta steg skapar följande variabler i execution context:

- `var-{{KEBAB_CASE}}-result` - Resultat från steget
- `var-{{KEBAB_CASE}}-timestamp` - Tidsstämpel när steget utfördes

## Felhantering

Runner hanterar fel på följande sätt:

1. **Validering**: Kontrollerar step-data innan utförande
2. **Retry-logik**: Försöker igen vid tillfälliga fel
3. **Logging**: Loggar alla fel för debugging
4. **Graceful degradation**: Fortsätter med nästa steg om möjligt

### Vanliga Fel

| Fel | Orsak | Lösning |
|-----|-------|---------|
| `API_KEY_MISSING` | API-nyckel saknas | Lägg till API-nyckel i miljövariabler |
| `NETWORK_ERROR` | Nätverksfel | Kontrollera internetanslutning |
| `INVALID_STEP_DATA` | Ogiltig step-data | Validera step-properties |

## Utveckling

### Lägga till Nytt Steg

1. Definiera step-typ i `shared/src/types/steps/`
2. Lägg till validator i `shared/src/validators/steps/`
3. Implementera step-executor i `stepExecutors/`
4. Uppdatera runner för att hantera ny typ
5. Skapa editor-komponent i frontend
6. Lägg till i toolbar-definitioner
7. Skriv tester

### Köra Tester

```bash
# Enhetstester
npm test

# Integrationstester
RUN_INTEGRATION_TESTS=true npm test

# Coverage
npm run test:coverage
```

### Debugging

Aktivera debug-logging:

```env
DEBUG={{KEBAB_CASE}}:*
LOG_LEVEL=debug
```

## Prestanda

### Benchmarks

| Operation | Genomsnittlig tid | Max tid |
|-----------|------------------|---------|
| `{{STEP_TYPE}}` | 500ms | 2s |

### Optimering

- Använd connection pooling för API-anrop
- Cacha resultat när möjligt
- Implementera timeout för långsamma operationer

## Säkerhet

- API-nycklar lagras säkert i miljövariabler
- All kommunikation sker över HTTPS
- Input valideras för att förhindra injection-attacker

## Troubleshooting

### Runner startar inte

1. Kontrollera miljövariabler
2. Verifiera nätverksanslutning
3. Kontrollera API-nyckel

### Steg misslyckas

1. Kontrollera step-data
2. Verifiera API-status
3. Kontrollera loggar för detaljer

## Bidrag

1. Forka repository
2. Skapa feature branch
3. Implementera ändringar
4. Lägg till tester
5. Skicka pull request

## Licens

MIT License - se LICENSE fil för detaljer.
