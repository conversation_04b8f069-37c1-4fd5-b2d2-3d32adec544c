// Customer types
export interface Customer {
  id: string;
  customerNumber: string;
  name: string;
  vismaNumber?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Customer request types
export interface CreateCustomerRequest {
  customerNumber: string;
  name: string;
  vismaNumber?: string;
}

export interface UpdateCustomerRequest {
  customerNumber?: string;
  name?: string;
  vismaNumber?: string;
}

// OAuth2 Provider types
export type OAuth2Provider = 'eEkonomi' | 'eAccounting' | 'Fortnox' | 'manual';

export interface OAuth2Config {
  clientId: string;
  clientSecret: string;
  authUrl: string;
  tokenUrl: string;
  scopes: string[];
  redirectUri: string;
}

// Customer token types
export interface CustomerToken {
  id: string;
  customerId: string;
  name: string;
  description?: string;
  provider: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  hasApiToken?: boolean;
  hasRefreshToken?: boolean;
  expiresAt?: Date;
  isExpired?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Customer token request types
export interface CreateCustomerTokenRequest {
  name: string;
  description?: string;
  provider: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
}

export interface UpdateCustomerTokenRequest {
  name?: string;
  description?: string;
  provider?: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
}

// OAuth2 specific request types
export interface InitiateOAuth2Request {
  customerId: string;
  provider: OAuth2Provider;
  tokenName: string;
  description?: string;
}

export interface OAuth2CallbackRequest {
  code: string;
  state: string;
}

export interface OAuth2TokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
}
