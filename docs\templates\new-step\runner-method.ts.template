// Template för runner-metod för nytt RPA-steg
// <PERSON><PERSON><PERSON> till denna metod i lämplig runner-klass

import { {{PASCAL_CASE}}Step } from '../../../shared/src/types/steps/{{STEP_CATEGORY}}';
import { ExecutionContext } from '../types';

/**
 * Utför {{STEP_TYPE}} steg
 */
async execute{{PASCAL_CASE}}Step(
  step: {{PASCAL_CASE}}Step,
  context: ExecutionContext,
  stepIndex?: number  // VIKTIGT: Lägg till för variabelkonsistens
): Promise<void> {
  try {
    this.logInfo(`Utför {{STEP_TYPE}}: ${step.name}`);

    // För Playwright-baserade steg:
    if (this.runnerType === 'playwright') {
      // Kontrollera att page finns
      if (!this.page) {
        throw new Error('Browser page är inte tillgänglig');
      }

      // Implementera step-logik här
      // Exempel för selector-baserade steg:
      // if (step.selector) {
      //   if (step.waitForSelector) {
      //     await this.page.waitForSelector(step.selector, { 
      //       timeout: step.timeout || 5000 
      //     });
      //   }
      //   
      //   // Utför åtgärd på element
      //   // await this.page.click(step.selector);
      //   // await this.page.fill(step.selector, step.value || '');
      //   // const text = await this.page.textContent(step.selector);
      // }
    }

    // För AI-baserade steg:
    if (this.runnerType === 'ai') {
      // Implementera AI-logik här
      // Exempel:
      // const response = await this.openaiClient.chat.completions.create({
      //   model: step.model || 'gpt-4o-mini',
      //   messages: [
      //     { role: 'user', content: step.prompt || '' }
      //   ]
      // });
      // 
      // const result = response.choices[0]?.message?.content || '';
      //
      // // För steg som skapar variabler - använd stepIndex för konsistens
      // const variableName = step.variableName || getDefaultVariableName('{{STEP_TYPE}}', stepIndex);
      // context.variables[variableName] = result;
    }

    // För API-baserade steg:
    if (this.runnerType === 'api') {
      // Implementera API-logik här
      // Exempel:
      // const response = await fetch(step.url || '', {
      //   method: step.method || 'GET',
      //   headers: step.headers || {},
      //   body: step.method !== 'GET' ? JSON.stringify(step.body) : undefined
      // });
      // 
      // const data = await response.json();
      //
      // // För steg som skapar variabler - använd stepIndex för konsistens
      // const variableName = step.variableName || getDefaultVariableName('{{STEP_TYPE}}', stepIndex);
      // context.variables[variableName] = data;
    }

    // Lägg till random delay (endast för Playwright)
    if (this.runnerType === 'playwright') {
      await this.randomDelay();
    }

    this.logInfo(`{{STEP_TYPE}} slutfört framgångsrikt`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Okänt fel';
    this.logError(`Fel vid {{STEP_TYPE}}: ${errorMessage}`);
    throw error;
  }
}

// Lägg till i runner-klassens executeStep metod:
// case '{{STEP_TYPE}}':
//   await this.execute{{PASCAL_CASE}}Step(step as {{PASCAL_CASE}}Step, context, stepIndex);
//   break;
//
// VIKTIGT: Glöm inte att skicka stepIndex för variabelkonsistens!
