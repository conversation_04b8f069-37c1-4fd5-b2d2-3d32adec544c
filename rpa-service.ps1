# RPA Service Management Script

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("install", "uninstall", "start", "stop", "restart", "status")]
    [string]$Action
)

$ServiceName = "RPA-Automation-App"
$ServiceDisplayName = "RPA Automation Application"
$ServiceDescription = "RPA Automation Platform with React Flow frontend and Node.js backend"
$WorkingDirectory = $PSScriptRoot

function Install-Service {
    Write-Host "Installing RPA service..."
    
    $ServiceScript = @"
Set-Location '$WorkingDirectory'
docker-compose -f docker-compose.prod.yml up
"@
    
    $ServiceScript | Out-File -FilePath "$WorkingDirectory\service-start.ps1" -Encoding UTF8
    
    # Använd NSSM (Non-Sucking Service Manager) för att skapa service
    # Ladda ner från: https://nssm.cc/download
    nssm install $ServiceName powershell.exe
    nssm set $ServiceName Arguments "-ExecutionPolicy Bypass -File `"$WorkingDirectory\service-start.ps1`""
    nssm set $ServiceName DisplayName $ServiceDisplayName
    nssm set $ServiceName Description $ServiceDescription
    nssm set $ServiceName Start SERVICE_AUTO_START
    
    Write-Host "Service installed successfully!"
}

function Uninstall-Service {
    Write-Host "Uninstalling RPA service..."
    Stop-Service $ServiceName -ErrorAction SilentlyContinue
    nssm remove $ServiceName confirm
    Remove-Item "$WorkingDirectory\service-start.ps1" -ErrorAction SilentlyContinue
    Write-Host "Service uninstalled successfully!"
}

function Start-RPAService {
    Write-Host "Starting RPA service..."
    Start-Service $ServiceName
    Write-Host "Service started!"
}

function Stop-RPAService {
    Write-Host "Stopping RPA service..."
    Stop-Service $ServiceName
    Set-Location $WorkingDirectory
    docker-compose -f docker-compose.prod.yml down
    Write-Host "Service stopped!"
}

function Restart-RPAService {
    Stop-RPAService
    Start-Sleep -Seconds 5
    Start-RPAService
}

function Get-ServiceStatus {
    $service = Get-Service $ServiceName -ErrorAction SilentlyContinue
    if ($service) {
        Write-Host "Service Status: $($service.Status)"
        
        # Docker container status
        Set-Location $WorkingDirectory
        Write-Host "`nContainer Status:"
        docker-compose -f docker-compose.prod.yml ps
    } else {
        Write-Host "Service not installed"
    }
}

switch ($Action) {
    "install" { Install-Service }
    "uninstall" { Uninstall-Service }
    "start" { Start-RPAService }
    "stop" { Stop-RPAService }
    "restart" { Restart-RPAService }
    "status" { Get-ServiceStatus }
}
