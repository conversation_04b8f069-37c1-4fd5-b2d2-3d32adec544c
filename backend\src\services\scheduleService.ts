import { FlowSchedule, CreateScheduleRequest, UpdateScheduleRequest, ScheduleQuery, generateId, CronExpressionInfo } from '@rpa-project/shared';
import { statements, db } from '../database/database';
import { parseExpression } from 'cron-parser';

export interface ScheduleStorage {
  findAll(query?: ScheduleQuery): Promise<FlowSchedule[]>;
  findById(id: string): Promise<FlowSchedule | null>;
  findByFlowId(flowId: string): Promise<FlowSchedule[]>;
  findEnabledSchedules(): Promise<FlowSchedule[]>;
  findSchedulesDueForExecution(currentTime: Date): Promise<FlowSchedule[]>;
  save(schedule: FlowSchedule): Promise<FlowSchedule>;
  update(id: string, updates: Partial<FlowSchedule>): Promise<FlowSchedule | null>;
  delete(id: string): Promise<boolean>;
  exists(id: string): Promise<boolean>;
}

class SqliteScheduleStorage implements ScheduleStorage {
  async findAll(query: ScheduleQuery = {}): Promise<FlowSchedule[]> {
    let sql = 'SELECT * FROM schedules';
    const params: any[] = [];
    const conditions: string[] = [];

    if (query.flowId) {
      conditions.push('flow_id = ?');
      params.push(query.flowId);
    }

    if (query.enabled !== undefined) {
      conditions.push('enabled = ?');
      params.push(query.enabled ? 1 : 0);
    }

    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }

    sql += ' ORDER BY created_at DESC';

    if (query.limit) {
      sql += ' LIMIT ?';
      params.push(query.limit);
      
      if (query.offset) {
        sql += ' OFFSET ?';
        params.push(query.offset);
      }
    }

    const rows = db.prepare(sql).all(...params) as any[];
    return rows.map(this.mapRowToSchedule);
  }

  async findById(id: string): Promise<FlowSchedule | null> {
    const row = statements.getScheduleById.get(id) as any;
    return row ? this.mapRowToSchedule(row) : null;
  }

  async findByFlowId(flowId: string): Promise<FlowSchedule[]> {
    const rows = statements.getSchedulesByFlowId.all(flowId) as any[];
    return rows.map(this.mapRowToSchedule);
  }

  async findEnabledSchedules(): Promise<FlowSchedule[]> {
    const rows = statements.getEnabledSchedules.all() as any[];
    return rows.map(this.mapRowToSchedule);
  }

  async findSchedulesDueForExecution(currentTime: Date): Promise<FlowSchedule[]> {
    const rows = statements.getSchedulesDueForExecution.all(currentTime.toISOString()) as any[];
    return rows.map(this.mapRowToSchedule);
  }

  async save(schedule: FlowSchedule): Promise<FlowSchedule> {
    if (!schedule.id) {
      schedule.id = generateId();
    }

    const now = new Date();
    schedule.createdAt = schedule.createdAt || now;
    schedule.updatedAt = now;

    // Calculate next run time
    if (schedule.cronExpression) {
      schedule.nextRunAt = this.calculateNextRunTime(schedule.cronExpression, schedule.timezone);
    }

    statements.insertSchedule.run(
      schedule.id,
      schedule.flowId,
      schedule.name,
      schedule.description || '',
      schedule.cronExpression,
      schedule.timezone || 'UTC',
      schedule.enabled ? 1 : 0,
      JSON.stringify(schedule.variables || {}),
      schedule.createdAt.toISOString(),
      schedule.updatedAt.toISOString(),
      schedule.nextRunAt?.toISOString() || null
    );

    return schedule;
  }

  async update(id: string, updates: Partial<FlowSchedule>): Promise<FlowSchedule | null> {
    const existing = await this.findById(id);
    if (!existing) return null;

    const updated = { ...existing, ...updates, updatedAt: new Date() };

    // Recalculate next run time if cron expression or timezone changed
    if (updates.cronExpression || updates.timezone) {
      updated.nextRunAt = this.calculateNextRunTime(updated.cronExpression, updated.timezone);
    }

    statements.updateSchedule.run(
      updated.name,
      updated.description || '',
      updated.cronExpression,
      updated.timezone || 'UTC',
      updated.enabled ? 1 : 0,
      JSON.stringify(updated.variables || {}),
      updated.updatedAt.toISOString(),
      updated.nextRunAt?.toISOString() || null,
      id
    );

    return updated;
  }

  async delete(id: string): Promise<boolean> {
    const result = statements.deleteSchedule.run(id);
    return result.changes > 0;
  }

  async exists(id: string): Promise<boolean> {
    const result = statements.scheduleExists.get(id);
    return !!result;
  }

  private mapRowToSchedule(row: any): FlowSchedule {
    return {
      id: row.id,
      flowId: row.flow_id,
      name: row.name,
      description: row.description,
      cronExpression: row.cron_expression,
      timezone: row.timezone,
      enabled: !!row.enabled,
      variables: JSON.parse(row.variables || '{}'),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      lastRunAt: row.last_run_at ? new Date(row.last_run_at) : undefined,
      nextRunAt: row.next_run_at ? new Date(row.next_run_at) : undefined
    };
  }

  private calculateNextRunTime(cronExpression: string, timezone: string = 'UTC'): Date | undefined {
    try {
      const interval = parseExpression(cronExpression, {
        tz: timezone,
        currentDate: new Date()
      });
      return interval.next().toDate();
    } catch (error) {
      console.error('Error calculating next run time:', error);
      return undefined;
    }
  }
}

// Global storage instance
const globalScheduleStorage = new SqliteScheduleStorage();

export class ScheduleService {
  private storage = globalScheduleStorage;

  async getSchedules(query: ScheduleQuery = {}): Promise<FlowSchedule[]> {
    return this.storage.findAll(query);
  }

  async getScheduleById(id: string): Promise<FlowSchedule | null> {
    return this.storage.findById(id);
  }

  async getSchedulesByFlowId(flowId: string): Promise<FlowSchedule[]> {
    return this.storage.findByFlowId(flowId);
  }

  async createSchedule(request: CreateScheduleRequest): Promise<FlowSchedule> {
    // Validate cron expression
    const cronInfo = this.validateCronExpression(request.cronExpression, request.timezone);
    if (!cronInfo.isValid) {
      throw new Error(`Invalid cron expression: ${cronInfo.error}`);
    }

    const schedule: FlowSchedule = {
      id: generateId(),
      flowId: request.flowId,
      name: request.name,
      description: request.description || '',
      cronExpression: request.cronExpression,
      timezone: request.timezone || 'UTC',
      enabled: request.enabled !== false,
      variables: request.variables || {},
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return this.storage.save(schedule);
  }

  async updateSchedule(id: string, request: UpdateScheduleRequest): Promise<FlowSchedule | null> {
    // Validate cron expression if provided
    if (request.cronExpression) {
      const cronInfo = this.validateCronExpression(request.cronExpression, request.timezone);
      if (!cronInfo.isValid) {
        throw new Error(`Invalid cron expression: ${cronInfo.error}`);
      }
    }

    return this.storage.update(id, request);
  }

  async deleteSchedule(id: string): Promise<boolean> {
    return this.storage.delete(id);
  }

  async getEnabledSchedules(): Promise<FlowSchedule[]> {
    return this.storage.findEnabledSchedules();
  }

  async getSchedulesDueForExecution(currentTime: Date = new Date()): Promise<FlowSchedule[]> {
    return this.storage.findSchedulesDueForExecution(currentTime);
  }

  async updateScheduleLastRun(id: string, lastRunAt: Date, nextRunAt?: Date): Promise<void> {
    statements.updateScheduleLastRun.run(
      lastRunAt.toISOString(),
      nextRunAt?.toISOString() || null,
      id
    );
  }

  validateCronExpression(cronExpression: string, timezone: string = 'UTC'): CronExpressionInfo {
    try {
      const interval = parseExpression(cronExpression, {
        tz: timezone,
        currentDate: new Date()
      });

      // Get next 5 runs for preview
      const nextRuns: Date[] = [];
      for (let i = 0; i < 5; i++) {
        nextRuns.push(interval.next().toDate());
      }

      return {
        expression: cronExpression,
        description: this.getCronDescription(cronExpression),
        nextRuns,
        isValid: true
      };
    } catch (error) {
      return {
        expression: cronExpression,
        description: '',
        nextRuns: [],
        isValid: false,
        error: error instanceof Error ? error.message : 'Invalid cron expression'
      };
    }
  }

  private getCronDescription(cronExpression: string): string {
    // Simple description generator - could be enhanced with a library like cronstrue
    const parts = cronExpression.split(' ');
    if (parts.length !== 5) return cronExpression;

    const [minute, hour, day, month, weekday] = parts;

    if (cronExpression === '* * * * *') return 'Every minute';
    if (cronExpression === '0 * * * *') return 'Every hour';
    if (cronExpression === '0 0 * * *') return 'Daily at midnight';
    if (cronExpression === '0 9 * * *') return 'Daily at 9 AM';
    if (cronExpression === '0 9 * * 1-5') return 'Weekdays at 9 AM';
    if (cronExpression === '0 9 * * 1') return 'Every Monday at 9 AM';

    return cronExpression;
  }
}
