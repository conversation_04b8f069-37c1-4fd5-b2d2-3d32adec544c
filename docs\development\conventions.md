# Kodkonventioner

Detta dokument beskriver kodkonventioner och best practices för RPA-projektet.

## Allmänna Principer

### 1. Konsistens

- Följ etablerade patterns i kodbasen
- Använd samma namngivningskonventioner överallt
- <PERSON><PERSON><PERSON> liknande kod strukturerad på samma sätt

### 2. Läsbarhet

- Skriv kod som är lätt att förstå
- Använd beskrivande namn för variabler och funktioner
- Kommentera komplex logik

### 3. Underhållbarhet

- Håll funktioner små och fokuserade
- Undvik duplicerad kod
- Separera concerns tydligt

## Namngivning

### TypeScript/JavaScript

```typescript
// Variabler och funktioner: camelCase
const userName = 'john_doe';
const getUserData = () => {};

// Klasser: PascalCase
class UserManager {}
class PlaywrightRunner {}

// Interfaces: PascalCase med I-prefix för interna interfaces
interface IRunner {}
interface UserData {}

// Konstanter: UPPER_SNAKE_CASE
const MAX_RETRY_COUNT = 3;
const DEFAULT_TIMEOUT = 5000;

// Enums: PascalCase
enum StepType {
  Navigate = 'navigate',
  Click = 'click'
}

// Typer: PascalCase
type RunnerType = 'playwright' | 'ai';
```

### Filer och Mappar

```
// Filer: PascalCase för komponenter, camelCase för utilities
UserManager.ts
PlaywrightRunner.ts
stepValidators.ts
apiHelpers.ts

// Mappar: kebab-case
step-editors/
flow-designer/
api-helpers/

// Test-filer: samma som källfil + .test
UserManager.test.ts
stepValidators.test.ts
```

### RPA-specifika Konventioner

```typescript
// Step-typer: camelCase
type StepType = 'navigate' | 'click' | 'extractText';

// Step-klasser: PascalCase + Step suffix
interface NavigateStep extends RpaStepBase {}
interface ClickStep extends RpaStepBase {}

// Runner-typer: camelCase
type RunnerType = 'playwright' | 'ai' | 'api';

// Runner-klasser: PascalCase + Runner suffix
class PlaywrightRunner implements IRunner {}
class AIRunner implements IRunner {}

// Variabler: var-{step-type}-{property}
'var-download-file'
'var-screenshot'
'var-ai-response'
```

## Filstruktur

### Shared Package

```
shared/src/
├── types/
│   ├── steps/
│   │   ├── index.ts          # Export alla + RpaStep union
│   │   ├── base.ts           # RpaStepBase
│   │   ├── navigation.ts     # Navigation-steg
│   │   └── interaction.ts    # Interaction-steg
│   ├── runners.ts            # Runner interfaces
│   └── index.ts              # Re-export allt
├── validators/
│   ├── steps/
│   │   ├── index.ts          # Main validateStep function
│   │   ├── navigation.ts     # Navigation validators
│   │   └── interaction.ts    # Interaction validators
│   └── index.ts              # Re-export allt
└── index.ts                  # Package entry point
```

### Backend

```
backend/src/
├── routes/                   # Express routes
├── services/                 # Business logic
├── runners/
│   ├── base/                # Bas-klasser
│   ├── playwright/          # Playwright runner
│   │   ├── PlaywrightRunner.ts
│   │   ├── stepExecutors/
│   │   └── index.ts
│   ├── registry/            # Runner registry
│   └── factory/             # Runner factory
└── utils/                   # Hjälpfunktioner
```

### Frontend

```
frontend/src/
├── components/
│   ├── flow-editor/
│   │   ├── step-editors/
│   │   │   ├── base/
│   │   │   ├── navigation/
│   │   │   └── interaction/
│   │   ├── step-definitions/
│   │   └── FlowEditor.tsx
│   └── common/              # Återanvändbara komponenter
├── hooks/                   # Custom hooks
├── services/                # API-anrop
└── utils/                   # Hjälpfunktioner
```

## TypeScript Konventioner

### Typer och Interfaces

```typescript
// Använd interfaces för objekt-shapes
interface UserData {
  id: string;
  name: string;
  email: string;
}

// Använd types för unions och primitives
type Status = 'pending' | 'completed' | 'failed';
type UserId = string;

// Generics: Enkla bokstäver för enkla fall
interface ApiResponse<T> {
  data: T;
  status: number;
}

// Beskrivande namn för komplexa generics
interface StepExecutor<TStep extends RpaStepBase> {
  execute(step: TStep): Promise<void>;
}
```

### Funktioner

```typescript
// Använd arrow functions för korta funktioner
const add = (a: number, b: number) => a + b;

// Använd function declarations för komplexa funktioner
function validateStep(step: RpaStep): string[] {
  // Komplex validering...
}

// Async/await över Promises
async function fetchUserData(id: string): Promise<UserData> {
  const response = await fetch(`/api/users/${id}`);
  return response.json();
}

// Explicit return types för publika APIs
export function createStep(type: string): RpaStep {
  // Implementation...
}
```

### Error Handling

```typescript
// Använd specifika error-typer
class ValidationError extends Error {
  constructor(message: string, public field: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

// Hantera fel gracefully
async function executeStep(step: RpaStep): Promise<void> {
  try {
    await performStep(step);
  } catch (error) {
    if (error instanceof ValidationError) {
      this.logError(`Validering misslyckades: ${error.message}`);
    } else {
      this.logError(`Oväntat fel: ${error}`);
    }
    throw error;
  }
}
```

## React Konventioner

### Komponenter

```typescript
// Functional components med TypeScript
interface UserCardProps {
  user: UserData;
  onEdit: (user: UserData) => void;
}

export const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
  const handleEdit = () => {
    onEdit(user);
  };

  return (
    <div className="user-card">
      <h3>{user.name}</h3>
      <button onClick={handleEdit}>Redigera</button>
    </div>
  );
};

// Default export för sidor, named export för komponenter
export default UserCard; // Endast för sidor
export { UserCard };      // För komponenter
```

### Hooks

```typescript
// Custom hooks börjar med 'use'
function useStepValidation(step: RpaStep) {
  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    const validationErrors = validateStep(step);
    setErrors(validationErrors);
  }, [step]);

  return { errors, isValid: errors.length === 0 };
}

// State updates
const [step, setStep] = useState<RpaStep>(initialStep);

const updateStep = (field: keyof RpaStep, value: any) => {
  setStep(prev => ({
    ...prev,
    [field]: value
  }));
};
```

### CSS Klasser

```css
/* Använd kebab-case för CSS-klasser */
.step-editor {
  padding: 16px;
}

.form-group {
  margin-bottom: 12px;
}

.button-primary {
  background-color: #fd746c;
}

/* BEM-notation för komplexa komponenter */
.step-toolbar__item {
  padding: 8px;
}

.step-toolbar__item--active {
  background-color: #f2e8e8;
}
```

## Kommentarer och Dokumentation

### JSDoc för Publika APIs

```typescript
/**
 * Validerar ett RPA-steg och returnerar eventuella fel
 * 
 * @param step - Steget som ska valideras
 * @returns Array med felmeddelanden, tom om inga fel
 * 
 * @example
 * ```typescript
 * const errors = validateStep({
 *   type: 'navigate',
 *   url: 'https://example.com'
 * });
 * ```
 */
export function validateStep(step: RpaStep): string[] {
  // Implementation...
}
```

### Inline Kommentarer

```typescript
// Använd kommentarer för att förklara VARFÖR, inte VAD
function calculateDelay(): number {
  // Använd random delay för att undvika bot-detection
  return Math.random() * 9000 + 1000;
}

// TODO: Implementera retry-logik
// FIXME: Hantera edge case när selector inte finns
// NOTE: Detta är en tillfällig lösning
```

## Testing Konventioner

### Test-struktur

```typescript
describe('StepValidator', () => {
  describe('validateNavigateStep', () => {
    test('ska acceptera giltig navigate step', () => {
      const step: NavigateStep = {
        id: 'test-1',
        type: 'navigate',
        name: 'Test Navigation',
        url: 'https://example.com'
      };

      const errors = validateNavigateStep(step);
      expect(errors).toHaveLength(0);
    });

    test('ska kräva URL', () => {
      const step: NavigateStep = {
        id: 'test-1',
        type: 'navigate',
        name: 'Test Navigation',
        url: ''
      };

      const errors = validateNavigateStep(step);
      expect(errors).toContain('URL är obligatorisk');
    });
  });
});
```

### Test-namn

```typescript
// Använd beskrivande test-namn på svenska
test('ska validera giltigt steg')
test('ska kräva obligatoriska fält')
test('ska hantera fel gracefully')
test('ska returnera korrekt resultat')
```

## Git Konventioner

### Commit Messages

```
feat: lägg till nytt extractText steg
fix: åtgärda timeout-problem i PlaywrightRunner
docs: uppdatera API-dokumentation
refactor: omstrukturera step-validators
test: lägg till tester för AIRunner
chore: uppdatera dependencies
```

### Branch Names

```
feature/extract-text-step
bugfix/playwright-timeout
hotfix/critical-security-issue
docs/api-documentation
refactor/step-validators
```

## Performance Best Practices

### Undvik Onödiga Re-renders

```typescript
// Använd React.memo för dyra komponenter
export const StepEditor = React.memo<StepEditorProps>(({ step, onStepChange }) => {
  // Component implementation
});

// Använd useCallback för event handlers
const handleStepChange = useCallback((field: string, value: any) => {
  onStepChange({ ...step, [field]: value });
}, [step, onStepChange]);
```

### Optimera API-anrop

```typescript
// Använd React Query för caching
const { data: flows, isLoading } = useQuery(
  ['flows', customerId],
  () => fetchFlows(customerId),
  { staleTime: 5 * 60 * 1000 } // 5 minuter
);

// Batch API-anrop när möjligt
const updateMultipleSteps = async (updates: StepUpdate[]) => {
  await api.post('/steps/batch-update', { updates });
};
```

## Säkerhet

### Input Validation

```typescript
// Validera all input
function sanitizeSelector(selector: string): string {
  // Ta bort potentiellt farliga tecken
  return selector.replace(/[<>]/g, '');
}

// Använd type guards
function isValidStepType(type: string): type is StepType {
  return ['navigate', 'click', 'fill'].includes(type);
}
```

### Secrets Management

```typescript
// Använd miljövariabler för secrets
const apiKey = process.env.OPENAI_API_KEY;
if (!apiKey) {
  throw new Error('OPENAI_API_KEY är obligatorisk');
}

// Logga aldrig secrets
this.logInfo(`API-anrop till ${endpoint}`); // OK
this.logInfo(`API-nyckel: ${apiKey}`);      // ALDRIG!
```

## Felhantering

### Graceful Degradation

```typescript
// Hantera fel utan att krascha hela systemet
async function executeStep(step: RpaStep): Promise<void> {
  try {
    await performStep(step);
  } catch (error) {
    // Logga fel
    this.logError(`Steg misslyckades: ${error}`);
    
    // Fortsätt med nästa steg om möjligt
    if (step.continueOnError) {
      return;
    }
    
    // Annars kasta fel vidare
    throw error;
  }
}
```

### Error Boundaries

```typescript
// Använd Error Boundaries i React
class StepEditorErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('StepEditor error:', error, errorInfo);
    // Rapportera till error tracking service
  }

  render() {
    if (this.state.hasError) {
      return <div>Något gick fel med step-editorn</div>;
    }
    return this.props.children;
  }
}
```
