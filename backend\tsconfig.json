{"extends": "../tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "commonjs", "moduleResolution": "node", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "../shared"}], "ts-node": {"esm": false}}