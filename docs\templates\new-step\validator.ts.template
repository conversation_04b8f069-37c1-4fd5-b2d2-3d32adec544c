// Template för validator för nytt RPA-steg
// <PERSON>ä<PERSON> till denna funktion i shared/src/validators/steps/{{STEP_CATEGORY}}.ts

import { {{PASCAL_CASE}}Step } from '../../types/steps/{{STEP_CATEGORY}}';

/**
 * Validerar {{STEP_TYPE}} steg
 */
export function validate{{PASCAL_CASE}}Step(step: {{PASCAL_CASE}}Step): string[] {
  const errors: string[] = [];

  // Grundläggande validering
  if (!step.type || step.type !== '{{STEP_TYPE}}') {
    errors.push('Steg-typ måste vara "{{STEP_TYPE}}"');
  }

  // Lägg till step-specifik validering här
  // Exempel för selector-baserade steg:
  // if (!step.selector?.trim()) {
  //   errors.push('Selector är obligatorisk');
  // }

  // Exempel för value-baserade steg:
  // if (!step.value?.trim()) {
  //   errors.push('Värde är obligatoriskt');
  // }

  // Exempel för timeout-validering:
  // if (step.timeout !== undefined && (step.timeout < 0 || step.timeout > 60000)) {
  //   errors.push('Timeout måste vara mellan 0 och 60000ms');
  // }

  // Exempel för AI-steg:
  // if (!step.prompt?.trim()) {
  //   errors.push('Prompt är obligatorisk för AI-steg');
  // }

  // Exempel för API-steg:
  // if (!step.url?.trim()) {
  //   errors.push('URL är obligatorisk för API-steg');
  // }
  // if (!['GET', 'POST', 'PUT', 'DELETE'].includes(step.method || '')) {
  //   errors.push('HTTP-metod måste vara GET, POST, PUT eller DELETE');
  // }

  return errors;
}

/**
 * Skapar ett nytt {{STEP_TYPE}} steg med standardvärden
 */
export function create{{PASCAL_CASE}}Step(): {{PASCAL_CASE}}Step {
  return {
    id: '',
    type: '{{STEP_TYPE}}',
    name: '{{STEP_DESCRIPTION}}',
    description: '',
    
    // Lägg till standardvärden för step-specifika properties
    // Exempel:
    // selector: '',
    // value: '',
    // timeout: 5000,
    // waitForSelector: true,
  };
}

// Lägg till i huvudvalidatorn (shared/src/validators/steps/index.ts):
// case '{{STEP_TYPE}}':
//   return validate{{PASCAL_CASE}}Step(step as {{PASCAL_CASE}}Step);

// Lägg till i createStepFromType funktionen:
// case '{{STEP_TYPE}}':
//   return create{{PASCAL_CASE}}Step();
