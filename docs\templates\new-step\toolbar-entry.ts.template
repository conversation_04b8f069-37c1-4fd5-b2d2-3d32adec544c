// Template för toolbar-entry för nytt RPA-steg
// <PERSON><PERSON>gg till i frontend/src/components/flow-editor/step-definitions/{{STEP_CATEGORY}}.ts

import { StepDefinition } from './types';

// Lägg till denna definition i stepDefinitions array:
export const {{STEP_TYPE}}Definition: StepDefinition = {
  type: '{{STEP_TYPE}}',
  name: '{{STEP_DESCRIPTION}}',
  description: 'Beskrivning av vad steget gör',
  category: '{{STEP_CATEGORY}}',
  
  // Välj lämplig ikon från Lucide React
  // Vanliga ikoner:
  // - Navigation: ArrowRight, RotateCcw, RotateCw, RefreshCw
  // - Interaction: MousePointer, Type, CheckSquare, Download
  // - Waiting: Clock, Timer, Pause
  // - Extraction: FileText, Camera, Download
  // - AI: Brain, Sparkles, MessageSquare
  // - API: Globe, Send, Database
  icon: 'MousePointer', // <PERSON>ndra till lämplig ikon
  
  // F<PERSON>rg för kategorin (används för gruppering)
  color: '#fd746c', // Anpassa efter kategori
  
  // Standardvärden när steget skapas
  defaultStep: {
    id: '',
    type: '{{STEP_TYPE}}',
    name: '{{STEP_DESCRIPTION}}',
    description: '',
    
    // Lägg till step-specifika standardvärden
    // Exempel:
    // selector: '',
    // value: '',
    // timeout: 5000,
    // waitForSelector: true,
    
    // För AI-steg:
    // prompt: '',
    // model: 'gpt-4o-mini',
    
    // För API-steg:
    // url: '',
    // method: 'GET',
    // headers: {},
  },
  
  // Validering för toolbar (snabb kontroll)
  isValid: (step: any) => {
    // Grundläggande validering
    if (!step.name?.trim()) return false;
    
    // Step-specifik validering
    // Exempel:
    // if (!step.selector?.trim()) return false;
    // if (!step.url?.trim()) return false;
    // if (!step.prompt?.trim()) return false;
    
    return true;
  },
  
  // Kort beskrivning för tooltip
  tooltip: 'Kort beskrivning av vad steget gör',
  
  // Vilken runner som krävs
  requiredRunner: '{{RUNNER_TYPE}}',
  
  // Om steget skapar variabler
  createsVariables: [
    // Exempel:
    // 'var-{{KEBAB_CASE}}-result',
    // 'var-{{KEBAB_CASE}}-response',
  ],
  
  // Om steget kräver variabler
  requiresVariables: [
    // Exempel för steg som använder andra stegs resultat:
    // 'var-download-file',
    // 'var-screenshot',
  ],
  
  // Exempel på användning (visas i dokumentation)
  examples: [
    {
      name: 'Grundläggande användning',
      description: 'Exempel på hur steget används',
      step: {
        id: 'example-1',
        type: '{{STEP_TYPE}}',
        name: 'Exempel {{STEP_DESCRIPTION}}',
        description: 'Detta är ett exempel',
        // Lägg till exempel-värden
      }
    }
  ]
};

// Lägg till i huvudlistan (frontend/src/components/flow-editor/step-definitions/index.ts):
// import { {{STEP_TYPE}}Definition } from './{{STEP_CATEGORY}}';
// 
// export const stepDefinitions: StepDefinition[] = [
//   // ... befintliga definitioner
//   {{STEP_TYPE}}Definition,
// ];
