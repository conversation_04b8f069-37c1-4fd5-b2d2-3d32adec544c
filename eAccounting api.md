## Attachemnt

post /v2/attachmentsCreate a attachment
Implementation Notes
Requires any of the following scopes:
ea:purchase, ea:accounting

Available in any of the following variants:
Pro, Standard, Invoicing, Bookkeeping, Solo

Response Class (Status 201)
ModelExample Value
AttachmentResultApi {
Id (string, optional, read only),
ContentType (string, optional),
DocumentId (string, optional),
AttachedDocumentType (integer, optional): 0 = None, 1 = SupplierInvoice, 2 = Receipt, 3 = Voucher, 4 = SupplierInvoiceDraft, 5 = AllocationPeriod, 6 = Transfer = ['0', '1', '2', '3', '4', '5', '6', '8'],
MessageThreads (Array[string], optional, read only): Fetch messages via GET /v2/messagethreads/{messageThreadId} ,
Notes (Array[string], optional, read only): Fetch notes via GET /v2/notes/{notesId} ,
PhotoSource (integer, optional, read only): Unknown = 0, VismaScannerApp = 1, CompanyEmail = 2, EmployeeEmail = 3, DragAndDrop = 100, eInvoice = 101, ScanPDF = 102, ScanLetter = 103, Integration = 105 = ['0', '1', '2', '3', '100', '101', '102', '103', '105', '106'],
FileName (string, optional),
TemporaryUrl (string, optional),
Comment (string, optional),
SupplierName (string, optional),
CurrencyCode (string, optional),
AmountInvoiceCurrency (number, optional),
Type (integer, optional): 0 = Invoice, 1 = Receipt, 2 = Document = ['0', '1', '2'],
AttachmentStatus (integer, optional): 0 = Matched, 1 = Unmatched = ['0', '1'],
UploadedBy (string, optional, read only),
ImageDate (string, optional, read only): Date when source image was uploaded ,
TransactionDate (string, optional, read only): The interpreted transaction date of the source image, eg invoicedate for a invoice, paymentdate for a reciept. Can be null if not interpreted.
}


Response Content Type 
application/json
Parameters
Parameter	Description	Parameter Type	Data Type
postedAttachment		body	
ModelExample Value
AttachmentUploadApi {
Id (string, optional),
ContentType (string): = ['image/jpeg' or 'image/png' or 'image/tiff' or 'application/pdf'] ,
FileName (string): Max length: 200 characters. Format: Allowed characters are alphabetic letters, numbers and characters - _ () ,
Data (string, optional): Format: Must be Base64 encoded byte array. ,
Url (string, optional): Must be public URL
}

----------

## Attachmentlinks

post /v2/attachmentlinksCreate new links between an existing document and a set of attachments.
Implementation Notes
Requires any of the following scopes:
ea:accounting, ea:purchase

Available in any of the following variants:
Pro, Standard, Invoicing, Bookkeeping, Solo

Response Class (Status 200)
ModelExample Value
AttachmentLinkApi {
DocumentId (string, optional): The id inside eAccounting of the corresponding linked document. Source: Get from endpoints corresponding to your document type ,
DocumentType (integer): 0 = None, 1 = SupplierInvoice, 2 = Receipt, 3 = Voucher, 4 = SupplierInvoiceDraft, 5 = AllocationPeriod, 6 = Transfer = ['0', '1', '2', '3', '4', '5', '6', '8'],
AttachmentIds (Array[string])
}


Response Content Type 
application/json
Parameters
Parameter	Description	Parameter Type	Data Type
attachmentLinks		body	
ModelExample Value
AttachmentLinkApi {
DocumentId (string, optional): The id inside eAccounting of the corresponding linked document. Source: Get from endpoints corresponding to your document type ,
DocumentType (integer): 0 = None, 1 = SupplierInvoice, 2 = Receipt, 3 = Voucher, 4 = SupplierInvoiceDraft, 5 = AllocationPeriod, 6 = Transfer = ['0', '1', '2', '3', '4', '5', '6', '8'],
AttachmentIds (Array[string])
}

----------

## Voucher

post /v2/vouchersCreate a voucher.
Implementation Notes
Requires any of the following scopes:
ea:accounting

Available in any of the following variants:
Pro, Standard, Bookkeeping, Invoicing, Solo, Pro, Standard, Bookkeeping, Solo

Response Class (Status 201)
ModelExample Value
VoucherApi {
Id (string, optional, read only): Purpose: Unique Id provided by eAccounting ,
VoucherDate (string): Format: yyyy-mm-dd ,
VoucherText (string): Max length: 1000 characters ,
Rows (Array[VoucherRowApi]),
NumberAndNumberSeries (string, optional, read only): Purpose: Returns the voucher number. ,
NumberSeries (string, optional): Purpose: Returns the number series. Use parameter useDefaultVoucherSeries with false value to set Series. ,
ImportedVoucherNumber (string, optional, read only): Purpose: Returns original voucher number if the voucher is imported from another system. ,
Attachments (AttachmentLinkApi, optional),
ModifiedUtc (string, optional),
VoucherType (integer, optional): 2 = ManualVoucher, 5 = BankAccountTransferDeposit, 6 = BankAccountTransferWithDrawal, 7 = PurchaseReceipt, 8 = VatReport, 9 = SieImport, 10 = BankTransactionDeposit, 11 = BankTransactionWithdrawal, 12 = SupplierInvoiceDebit, 13 = SupplierInvoiceCredit, 14 = CustomerInvoiceDebit, 15 = CustomerInvoiceCredit, 16 = ClaimOnCardAcquirer, 17 = TaxReturn, 18 = AllocationPeriod, 19 = AllocationPeriodCorrection, 20 = InventoryEvent, 21 = EmployerReport, 22 = Payslip, 23 = CustomerQuickInvoiceDebit, 24 = CustomerQuickInvoiceCredit, 25 = SupplierQuickInvoiceDebit, 26 = SupplierQuickInvoiceCredit, 27 = IZettleVoucher = ['2', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34'],
SourceId (string, optional, read only),
CreatedUtc (string, optional, read only)
}
VoucherRowApi {
AccountNumber (integer),
AccountDescription (string, optional, read only),
DebitAmount (number, optional): Format: Max 2 decimals ,
CreditAmount (number, optional): Format: Max 2 decimals ,
TransactionText (string, optional): Max length: 60 characters ,
CostCenterItemId1 (string, optional): Source: Get from /v2/costcenters ,
CostCenterItemId2 (string, optional): Source: Get from /v2/costcenters ,
CostCenterItemId3 (string, optional): Source: Get from /v2/costcenters ,
VatCodeId (string, optional),
VatCodeAndPercent (string, optional),
VatAmount (number, optional),
Quantity (number, optional): Format: Max 2 decimals Purpose: This feature is for dutch companies only which enabled agriculture support ,
Weight (number, optional): Format: Max 2 decimals Purpose: This feature is for dutch companies only which enabled agriculture support ,
DeliveryDate (string, optional): Format: YYYY-MM-DD Purpose: This feature is for dutch companies only which enabled agriculture support ,
HarvestYear (integer, optional): Purpose: This feature is for dutch companies only which enabled agriculture support ,
ProjectId (string, optional): Source: Get from /v2/projects
}
AttachmentLinkApi {
DocumentId (string, optional): The id inside eAccounting of the corresponding linked document. Source: Get from endpoints corresponding to your document type ,
DocumentType (integer): 0 = None, 1 = SupplierInvoice, 2 = Receipt, 3 = Voucher, 4 = SupplierInvoiceDraft, 5 = AllocationPeriod, 6 = Transfer = ['0', '1', '2', '3', '4', '5', '6', '8'],
AttachmentIds (Array[string])
}


Response Content Type 
application/json
Parameters
Parameter	Description	Parameter Type	Data Type
apiVoucher		body	
ModelExample Value
VoucherApi {
Id (string, optional, read only): Purpose: Unique Id provided by eAccounting ,
VoucherDate (string): Format: yyyy-mm-dd ,
VoucherText (string): Max length: 1000 characters ,
Rows (Array[VoucherRowApi]),
NumberAndNumberSeries (string, optional, read only): Purpose: Returns the voucher number. ,
NumberSeries (string, optional): Purpose: Returns the number series. Use parameter useDefaultVoucherSeries with false value to set Series. ,
ImportedVoucherNumber (string, optional, read only): Purpose: Returns original voucher number if the voucher is imported from another system. ,
Attachments (AttachmentLinkApi, optional),
ModifiedUtc (string, optional),
VoucherType (integer, optional): 2 = ManualVoucher, 5 = BankAccountTransferDeposit, 6 = BankAccountTransferWithDrawal, 7 = PurchaseReceipt, 8 = VatReport, 9 = SieImport, 10 = BankTransactionDeposit, 11 = BankTransactionWithdrawal, 12 = SupplierInvoiceDebit, 13 = SupplierInvoiceCredit, 14 = CustomerInvoiceDebit, 15 = CustomerInvoiceCredit, 16 = ClaimOnCardAcquirer, 17 = TaxReturn, 18 = AllocationPeriod, 19 = AllocationPeriodCorrection, 20 = InventoryEvent, 21 = EmployerReport, 22 = Payslip, 23 = CustomerQuickInvoiceDebit, 24 = CustomerQuickInvoiceCredit, 25 = SupplierQuickInvoiceDebit, 26 = SupplierQuickInvoiceCredit, 27 = IZettleVoucher = ['2', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34'],
SourceId (string, optional, read only),
CreatedUtc (string, optional, read only)
}
VoucherRowApi {
AccountNumber (integer),
AccountDescription (string, optional, read only),
DebitAmount (number, optional): Format: Max 2 decimals ,
CreditAmount (number, optional): Format: Max 2 decimals ,
TransactionText (string, optional): Max length: 60 characters ,
CostCenterItemId1 (string, optional): Source: Get from /v2/costcenters ,
CostCenterItemId2 (string, optional): Source: Get from /v2/costcenters ,
CostCenterItemId3 (string, optional): Source: Get from /v2/costcenters ,
VatCodeId (string, optional),
VatCodeAndPercent (string, optional),
VatAmount (number, optional),
Quantity (number, optional): Format: Max 2 decimals Purpose: This feature is for dutch companies only which enabled agriculture support ,
Weight (number, optional): Format: Max 2 decimals Purpose: This feature is for dutch companies only which enabled agriculture support ,
DeliveryDate (string, optional): Format: YYYY-MM-DD Purpose: This feature is for dutch companies only which enabled agriculture support ,
HarvestYear (integer, optional): Purpose: This feature is for dutch companies only which enabled agriculture support ,
ProjectId (string, optional): Source: Get from /v2/projects
}
AttachmentLinkApi {
DocumentId (string, optional): The id inside eAccounting of the corresponding linked document. Source: Get from endpoints corresponding to your document type ,
DocumentType (integer): 0 = None, 1 = SupplierInvoice, 2 = Receipt, 3 = Voucher, 4 = SupplierInvoiceDraft, 5 = AllocationPeriod, 6 = Transfer = ['0', '1', '2', '3', '4', '5', '6', '8'],
AttachmentIds (Array[string])
}
useAutomaticVatCalculation	
Default value: false. Set to true and specify the sales or purchase gross amount and vat rows will be added automatically.

query	boolean
useDefaultVatCodes	
Default value: True. Set to false and override default vatcodes on all rows in the request.

query	boolean
useDefaultVoucherSeries	
Default value: True. Set to false and override default voucher series (alphabetic character before number).

query	boolean

--------

## Authentication

Applications connect to Bookkeeping & Invoicing/eAccounting and Payroll API using OAuth2, the standard used by most APIs for authenticating and authorizing users. The following documentation will guide you how to authorize your application to act on behalf of a user (also referred to as the resource owner).

The OAuth2 flow is generally initiated by a user clicking a “Connect with Bookkeeping & Invoicing/eAccounting/Payroll” button in your application (web application, mobile app or desktop application). The end result of the OAuth2 flow is a token your application will use to access protected resources in the API's on behalf of the user. The token is unique to each application/user combination.

Both Bookkeeping & Invoicing/eAccounting and Payroll share the same Identity Server. Therefor the same token can be used towards both API's as long as the required scopes are authenticated.

Scopes
You need to provide a list of requested scopes in the authentication request. API endpoints are related to a scope, eg. sales related endpoints requires the sales scope.

Scope

Access

Description

ea:api	
required_ (For Bookkeeping & Invoicing/eAccounting integrations)
Must be used to get access to Bookkeeping & Invoicing/eAccounting API

offline_access	
required

Must be used to be able to receive a refresh token

ea:sales	
optional

Full access to sales resources

ea:sales_readonly	
optional

Read-only access to sales resources

ea:accounting	
optional

Full access to accounting resources

ea:accounting_readonly	
optional

Read-only access to accounting resources

ea:purchase	
optional

Full access to purchase resources

ea:purchase_readonly	
optional

Read-only access to purchase resources

vls:api	
required_ (For Payroll integrations)
Must be used to get access to Payroll API

The Server-side Flow
1. User Authentication
When the user clicks a “Connect with Bookkeeping & Invoicing/eAccounting/Payroll” button in your application, your application should make a request towards our authorization endpoint. In that request you need to provide client id, redirect uri, scopes, state, response type and the login prompt.

Parameter Name

Required

Description

client_id	
required

Provided to you upon registration for the Spiris Partner Programme

redirect_uri	
required

Callback uri that you have registered upon registration for the Spiris Partner Programme

scope	
required

Permissions to request, see below for more information. If passing multiple scopes, separate them using %20 (URL-encoded space character) or + character

response_type	
required

The grant type requested, for server-side flow it is always code

state	
recommended

A unique string that is passed back upon completion. It should be used to avoid cross-site forgery attacks by passing in a value that’s unique to the user you’re authenticating and checking it when authorize completes

prompt	
recommended

Set select_account to ignore the standard company setting in Visma Online and always prompt the user with the selection of company. This is highly recommended.

Set prompt=login to force user login.

These two prompt values can be used in combination with a + sign as a separator. The query will then look like this: prompt=login+select_account

acr_values	
recommended

Set acr_values=service:44643EB1-3F76-4C1C-A672-402AE8085934 to show only the companies that have access to Bookkeeping & Invoicing/eAccounting/Payroll in the company selector.

cURL

## Example on how the authentication request can look like

curl -X GET 'https://identity.vismaonline.com/connect/authorize?client_id=<client_id>&redirect_uri=<redirect_uri>&scope=ea:api%20offline_access%20ea:sales&state=<state_string>&response_type=code&prompt=select_account&acr_values=service:44643EB1-3F76-4C1C-A672-402AE8085934'
2. App Authorization & Redirect
Once Visma Authorization Server has successfully authenticated the user, the server will prompt them to authorize the application.

If the user clicks “Allow”, your application will be authorized. The Identity provider will redirect the user’s browser via HTTP 302 to the redirect_uri with an authorization code.

The authorization code is one time use and is valid for 5 minutes, in order to exchange it for access token and refresh token, which will be explained in the next section.

<code>your-redirect-uri/?code=<authorization_code></code>

Note: Keep in mind that fragments (#) are not allowed in redirect URIs when using OAuth 2.0.
Any part of the URI after a # is not sent to the server and will be ignored, which may cause issues during the authentication flow.

3. App Authentication
When you have recieved the authorization code, next step is to exchange that for a set of tokens. These tokens are needed in order to make authorized requests towards the authenticated company.

Issue a post request like the example below. Note that the Authorization header in this request is client_id followed by a punctiuation sign : followed by client_secret. Then Base64 encode that string and provide, like in the example.

Example Request

POST /connect/token HTTP/1.1
Authorization: Basic base64(client_id:client_secret)
Content-Type: application/x-www-form-urlencoded;charset=UTF-8

"grant_type=authorization_code&code=<authorization_code>&redirect_uri=<redirect_uri>"
Example Response

HTTP/1.1 200 OK
Content-Type: application/json;charset=UTF-8

{
  "access_token": <access_token>,
  "refresh_token": <refresh_token>,
  "token_type":"bearer",
  "expires_in":3600
}
We recommend that you save all information in the response on your side, in order to make your integration as smooth as possible.

4. Using The Access Token
The access token is your way in to the API, and must be provided in all requests to the API. Since it's a bearer type of token, it should be provided as one aswell.

cURL

## This is a authorized request towards the API. The token is faked in this example.

curl -X GET -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImEzck1VZ01Gdjl0UGNsTGE2eUYzekFrZnF1RSIsImtpZCI6ImEzck1VZ01Gdjl0UGNsTGE2eUYzekFrZnF1RSJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LIIRPjwtDEtCYD1Sz2jlKDX9MClLDDXYSyfhjPKyGxOfXzBzfgNHfFmp6fa9AZ7RJZ9QFtbQ0vFj_QJgsWia6jJp_-is1LX_1cqB9G3XxbOuzJuIAVtd9gRgT16OVJ_vmwnll2mF2HD0XcYkJ5oppogF9CGJ3LY-stC8HaPkrW8eYEQ5vrJJjhC7RExVW_HXWRnb_mams7bK3lx2odFzCRglHXMNOgSgnbW0oOWRPCmWoP5_RY59t_9xB3GQ5hFQ5IxvVITJomr_uSk8iqF1m3aJn5oGfXjxH-63-0tVXEbnBtci-n15VG3K-Wh2Wbo_OWe434N9Euz-uXMJSCPgDg" \
'https://eaccountingapi.vismaonline.com/v2/companysettings'
60 minutes after the Access Token was issued, it will expire. In the next section we will go through how to refresh it using the refresh token.

5. Using The Refresh Token
When the Access Token has expired, it's time to make sure you have a new before next request in order to prevent unauthorized requests.

The request needed in this case is quite similar to the app authentication request but with a few modifications.

Change grant_type value to refresh_token. Also provide a property called refresh_token instead of code. The value of the refresh_token property is the refresh token itself. The response properties are identical to the previous request.

Example Request

POST /connect/token HTTP/1.1
Authorization: Basic base64(client_id:client_secret)
Content-Type: application/x-www-form-urlencoded;charset=UTF-8

"grant_type=refresh_token&refresh_token=<refresh_token>"
Example Response

HTTP/1.1 200 OK
Content-Type: application/json;charset=UTF-8

{
  "access_token": <access_token>,
  "refresh_token": <refresh_token>,
  "token_type":"bearer",
  "expires_in":3600
}
📘
Refresh token validity

The refresh token remains valid for two years from the date it is issued. However, please be aware that if the user changes their password, the refresh token will become invalid.

6. Token Revocation
If you would like to revoke a issued Refresh Token, you can use the Revocation endpoint. The provided Token will be invalidated for further use.

The Authorization towards this enpoint is the same as in the App Authentication request, client_id followed by a punctiuation sign : followed by client_secret. Then Base64 encode that string and provide in the request.

The request body contains two properties, token and token_type_hint. Token is required, but token_type_hint isn't. If token_type_hint isn't provided, the IdentityServer will try to figure out if it's a Access Token or Refresh Token. In the example below, I choose to let the IdentityServer know that it's a Refresh Token I would like to revoke.

Example Request

POST /connect/revocation HTTP/1.1
Authorization: Basic base64(client_id:client_secret)
Content-Type: application/x-www-form-urlencoded;charset=UTF-8

"token=<refresh_token>&token_type_hint=refresh_token"
If successful, you will get a blank response with status code 200 OK.

Example Response

HTTP/1.1 200 OK
Content-Type: application/json;charset=UTF-8

