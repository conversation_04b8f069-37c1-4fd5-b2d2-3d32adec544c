# Multi-stage build for better optimization
# Build stage
FROM node:20-alpine as builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY shared/package*.json ./shared/

# Install build dependencies for native modules
RUN apk add --no-cache python3 make g++

# Install all dependencies (including dev dependencies for building)
RUN npm ci

# Copy shared types and build
COPY shared/ ./shared/
COPY tsconfig.json ./
WORKDIR /app/shared
RUN npm run build

# Copy backend source and build
WORKDIR /app
COPY backend/ ./backend/
WORKDIR /app/backend
RUN npm run build

# Production stage
FROM mcr.microsoft.com/playwright:v1.40.0-focal

# Create non-root user for security
RUN groupadd -r rpauser && useradd -r -g rpauser -m -d /home/<USER>

# Set working directory
WORKDIR /app

# Copy package files for production dependencies
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY shared/package*.json ./shared/

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built shared package
COPY --from=builder /app/shared/dist ./shared/dist
COPY --from=builder /app/shared/package.json ./shared/

# Copy built backend
COPY --from=builder /app/backend/dist ./backend/dist
COPY --from=builder /app/backend/package.json ./backend/

# Create directories with proper permissions
RUN mkdir -p /app/screenshots /app/downloads /app/data && \
    chown -R rpauser:rpauser /app

# Install only Chromium browser (most commonly used) and curl for healthcheck
RUN npx playwright install chromium && \
    npx playwright install-deps chromium && \
    apt-get update && apt-get install -y curl && apt-get clean

# Switch to non-root user
USER rpauser

# Expose port
EXPOSE 3002

# Set environment variables
ENV NODE_ENV=production
ENV REDIS_URL=redis://redis:6379
ENV HOME=/home/<USER>
ENV npm_config_cache=/tmp/.npm

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3002/health || exit 1

# Start the application
WORKDIR /app/backend
CMD ["npm", "start"]
