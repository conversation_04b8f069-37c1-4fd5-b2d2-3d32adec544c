// Template för ny runner-klass
// Skapa som backend/src/runners/{{KEBAB_CASE}}/{{PASCAL_CASE}}Runner.ts

import { BaseRunner } from '../base/BaseRunner';
import { RpaStep } from '../../../shared/src/types/steps';
import { ExecutionContext } from '../types';

/**
 * {{PASCAL_CASE}} Runner - Hanterar {{STEP_DESCRIPTION}} steg
 */
export class {{PASCAL_CASE}}Runner extends BaseRunner {
  public readonly runnerType = '{{KEBAB_CASE}}' as const;
  
  // Lägg till runner-specifika properties
  // Exempel:
  // private apiClient?: SomeApiClient;
  // private config: RunnerConfig;

  constructor() {
    super();
    this.initializeRunner();
  }

  /**
   * Initialiserar runner med nödvändiga resurser
   */
  private async initializeRunner(): Promise<void> {
    try {
      // Initialisera runner-specifika resurser
      // Exempel:
      // this.apiClient = new SomeApiClient({
      //   apiKey: process.env.API_KEY,
      //   baseUrl: process.env.API_BASE_URL
      // });
      
      this.logInfo('{{PASCAL_CASE}}Runner initialiserad');
    } catch (error) {
      this.logError(`Fel vid initialisering av {{PASCAL_CASE}}Runner: ${error}`);
      throw error;
    }
  }

  /**
   * Kontrollerar om runner kan hantera ett specifikt steg
   */
  public canExecuteStep(step: RpaStep): boolean {
    // Definiera vilka steg-typer denna runner kan hantera
    const supportedSteps = [
      // Lägg till steg-typer som denna runner hanterar
      // '{{STEP_TYPE}}',
      // 'anotherStepType',
    ];
    
    return supportedSteps.includes(step.type);
  }

  /**
   * Utför ett RPA-steg
   */
  public async executeStep(step: RpaStep, context: ExecutionContext): Promise<void> {
    if (!this.canExecuteStep(step)) {
      throw new Error(`{{PASCAL_CASE}}Runner kan inte hantera steg av typ: ${step.type}`);
    }

    this.logInfo(`Utför ${step.type}: ${step.name}`);

    try {
      switch (step.type) {
        // Lägg till case för varje steg-typ som runner hanterar
        // case '{{STEP_TYPE}}':
        //   await this.execute{{PASCAL_CASE}}Step(step as {{PASCAL_CASE}}Step, context);
        //   break;

        default:
          throw new Error(`Ostödd steg-typ: ${step.type}`);
      }

      this.logInfo(`${step.type} slutfört framgångsrikt`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Okänt fel';
      this.logError(`Fel vid ${step.type}: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Startar runner (kallas innan första steget)
   */
  public async start(): Promise<void> {
    try {
      this.logInfo('Startar {{PASCAL_CASE}}Runner');
      
      // Utför start-logik
      // Exempel:
      // await this.apiClient?.authenticate();
      // await this.setupSession();
      
      this.logInfo('{{PASCAL_CASE}}Runner startad');
    } catch (error) {
      this.logError(`Fel vid start av {{PASCAL_CASE}}Runner: ${error}`);
      throw error;
    }
  }

  /**
   * Stoppar runner (kallas efter sista steget eller vid fel)
   */
  public async stop(): Promise<void> {
    try {
      this.logInfo('Stoppar {{PASCAL_CASE}}Runner');
      
      // Utför cleanup-logik
      // Exempel:
      // await this.apiClient?.disconnect();
      // await this.cleanupResources();
      
      this.logInfo('{{PASCAL_CASE}}Runner stoppad');
    } catch (error) {
      this.logError(`Fel vid stopp av {{PASCAL_CASE}}Runner: ${error}`);
      // Logga fel men kasta inte - cleanup ska alltid lyckas
    }
  }

  /**
   * Hämtar runner-status
   */
  public getStatus(): { isReady: boolean; details: string } {
    // Kontrollera runner-status
    // Exempel:
    // const isApiConnected = this.apiClient?.isConnected() || false;
    // return {
    //   isReady: isApiConnected,
    //   details: isApiConnected ? 'API ansluten' : 'API inte ansluten'
    // };
    
    return {
      isReady: true,
      details: '{{PASCAL_CASE}}Runner redo'
    };
  }

  // Lägg till step-specifika metoder här
  // Exempel:
  // private async execute{{PASCAL_CASE}}Step(
  //   step: {{PASCAL_CASE}}Step, 
  //   context: ExecutionContext
  // ): Promise<void> {
  //   // Implementera step-logik
  // }
}
