import { ValidationResult, ValidationError } from '../utils';

export function validateSelector(selector: string): ValidationResult {
  const errors: ValidationError[] = [];

  if (!selector || selector.trim() === '') {
    errors.push({
      field: 'selector',
      message: 'Selector cannot be empty',
      
    });
    return { isValid: false, errors };
  }

  // Basic CSS selector validation
  try {
    // This is a simple check - in a real browser environment
    // we could use document.querySelector to validate
    if (selector.includes('>>') || selector.includes('xpath=')) {
      // Playwright-specific selectors are valid
      return { isValid: true, errors: [] };
    }
    
    // Basic CSS selector patterns
    const cssPattern = /^[a-zA-Z0-9\s\[\]="'.:>#_-]+$/;
    if (!cssPattern.test(selector)) {
      errors.push({
        field: 'selector',
        message: 'Invalid selector format',
        
      });
    }
  } catch {
    errors.push({
      field: 'selector',
      message: 'Invalid selector syntax',
      
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
