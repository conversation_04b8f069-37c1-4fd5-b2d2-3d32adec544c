# Server Configuration
PORT=3002
NODE_ENV=development

# Encryption Configuration
MASTER_KEY_PATH=../data/master.key

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Playwright Configuration
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_BROWSER=chromium

# Queue Configuration
QUEUE_NAME=rpa-flows
QUEUE_CONCURRENCY=5

# LLM Provider Configuration
LLM_PROVIDER=openai                   # 'openai' | 'azure'
LLM_DEFAULT_MODEL=gpt-4o-mini        # Default model for all AI calls
LLM_FALLBACK_MODEL=gpt-4o-mini       # Fallback if default fails

# OpenAI Configuration (existing)
OPENAI_API_KEY=your_openai_api_key

# Azure OpenAI Configuration (new)
AZURE_OPENAI_API_KEY=your_azure_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Azure Model Deployments
AZURE_MODEL_GPT4O_MINI=gpt-4o-mini-deployment
AZURE_MODEL_O1_MINI=o1-mini-deployment

# OAuth2 Configuration
# eEkonomi (Visma) OAuth2 Settings
EEKONOMI_CLIENT_ID=your_eekonomi_client_id
EEKONOMI_CLIENT_SECRET=your_eekonomi_client_secret
EEKONOMI_REDIRECT_URI=http://localhost:3002/api/oauth2/callback/eEkonomi

# Fortnox OAuth2 Settings
FORTNOX_CLIENT_ID=your_fortnox_client_id
FORTNOX_CLIENT_SECRET=your_fortnox_client_secret
FORTNOX_REDIRECT_URI=http://localhost:3002/api/oauth2/callback/Fortnox
