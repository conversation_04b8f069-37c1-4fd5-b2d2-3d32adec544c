import { useState } from 'react'
import { validateStep, RpaStep } from '@rpa-project/shared'
import type {
  FortnoxCreateVoucherStep,
  FortnoxUploadFileStep,
  FortnoxAttachFileToVoucherStep,
  FortnoxUploadAndCreateVoucherStep,
  EAccountingUploadFileStep,
  EAccountingCreateVoucherStep,
  EAccountingAttachFileToVoucherStep,
  EAccountingUploadAndCreateVoucherStep
} from '@rpa-project/shared/dist/esm/types/steps/api'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'
import { TextAreaField, VariableField, SelectField } from '../base/FieldComponents'

type APIStep = FortnoxCreateVoucherStep | FortnoxUploadFileStep | FortnoxAttachFileToVoucherStep | FortnoxUploadAndCreateVoucherStep | EAccountingUploadFileStep | EAccountingCreateVoucherStep | EAccountingAttachFileToVoucherStep | EAccountingUploadAndCreateVoucherStep

interface APIStepEditorProps extends BaseStepEditorProps {
  step: RpaStep
}

export function APIStepEditor({
  step,
  onSave,
  onCancel,
  compact = false,
  steps = [],
  currentStepIndex = 0
}: APIStepEditorProps) {
  const [editedStep, setEditedStep] = useState<APIStep>({ ...step } as unknown as APIStep)
  const [errors, setErrors] = useState<string[]>([])

  const handleSave = () => {
    const validation = validateStep(editedStep as unknown as RpaStep)
    if (!validation.isValid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep as unknown as RpaStep)
  }

  const updateStep = (updates: Partial<APIStep>) => {
    setEditedStep((prev: APIStep) => ({ ...prev, ...updates } as APIStep))
  }

  const renderFortnoxCreateVoucherFields = () => {
    const fortnoxStep = editedStep as FortnoxCreateVoucherStep

    return (
      <>
        <TextAreaField
          label="Beskrivning"
          value={fortnoxStep.description || ''}
          onChange={(value) => updateStep({ description: value })}
          placeholder="Beskrivning av verifikationen"
          compact={compact}
        />

        <VariableField
          label="Input-variabel"
          value={fortnoxStep.inputVariable || ''}
          onChange={(value) => updateStep({ inputVariable: value })}
          placeholder="Välj variabel med data för verifikationen"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <TextAreaField
          label="AI-prompt"
          value={fortnoxStep.aiPrompt || ''}
          onChange={(value) => updateStep({ aiPrompt: value })}
          placeholder="Beskriv hur AI:n ska skapa verifikationsraderna, t.ex. 'Skapa en inköpsverifikation med moms'"
          compact={compact}
        />

        <div style={{ display: 'grid', gridTemplateColumns: compact ? '1fr' : '1fr 1fr 1fr', gap: '1rem' }}>
          <SelectField
            label="Verifikationsserie"
            value={fortnoxStep.voucherSeries || 'A'}
            onChange={(value) => updateStep({ voucherSeries: value })}
            options={[
              { value: 'A', label: 'A - Allmän' },
              { value: 'B', label: 'B - Bank' },
              { value: 'C', label: 'C - Kassa' },
              { value: 'D', label: 'D - Dagsrapporter' },
              { value: 'Z', label: 'Z - Övrigt' }
            ]}
            compact={compact}
          />

          <VariableField
            label="Transaktionsdatum"
            value={fortnoxStep.transactionDate || ''}
            onChange={(value) => updateStep({ transactionDate: value })}
            placeholder="YYYY-MM-DD eller välj variabel"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />

          <VariableField
            label="Resultat-variabel"
            value={fortnoxStep.variableName || 'var-fortnox-voucher'}
            onChange={(value) => updateStep({ variableName: value })}
            placeholder="Variabelnamn för verifikationsresultat"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />
        </div>

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#f0f9ff',
          borderRadius: '0.5rem',
          border: '1px solid #0ea5e9'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>🤖</span>
            <span style={{ fontWeight: '500', color: '#0369a1' }}>AI-driven verifikationsskapande</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#0369a1',
            lineHeight: '1.4'
          }}>
            AI:n kommer att analysera input-data och skapa balanserade verifikationsrader automatiskt baserat på din prompt och Fortnox kontoplan.
          </p>
        </div>
      </>
    )
  }

  const renderFortnoxUploadFileFields = () => {
    const uploadStep = editedStep as FortnoxUploadFileStep

    return (
      <>
        <VariableField
          label="Fil-variabel"
          value={uploadStep.inputVariable || ''}
          onChange={(value) => updateStep({ inputVariable: value })}
          placeholder="Välj variabel med base64-filinnehåll"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <div style={{ display: 'grid', gridTemplateColumns: compact ? '1fr' : '1fr 1fr', gap: '1rem' }}>
          <VariableField
            label="Filnamn (valfritt)"
            value={uploadStep.filename || ''}
            onChange={(value) => updateStep({ filename: value })}
            placeholder="Anpassat filnamn eller lämna tomt för automatiskt"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />

          <VariableField
            label="Resultat-variabel"
            value={uploadStep.variableName || 'var-fortnox-file'}
            onChange={(value) => updateStep({ variableName: value })}
            placeholder="Variabelnamn för filresultat"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />
        </div>

        <TextAreaField
          label="Beskrivning (valfritt)"
          value={uploadStep.description || ''}
          onChange={(value) => updateStep({ description: value })}
          placeholder="Beskrivning av filen"
          compact={compact}
        />

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#f0fdf4',
          borderRadius: '0.5rem',
          border: '1px solid #22c55e'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>📤</span>
            <span style={{ fontWeight: '500', color: '#15803d' }}>Filuppladdning till Fortnox</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#15803d',
            lineHeight: '1.4'
          }}>
            Filen kommer att laddas upp till Fortnox arkiv och returnera ett fil-ID som kan användas för att koppla till verifikationer.
          </p>
        </div>
      </>
    )
  }

  const renderFortnoxAttachFileToVoucherFields = () => {
    const attachStep = editedStep as FortnoxAttachFileToVoucherStep

    return (
      <>
        <VariableField
          label="Fil-ID variabel"
          value={attachStep.fileIdVariable || ''}
          onChange={(value) => updateStep({ fileIdVariable: value })}
          placeholder="Välj variabel med fil-ID från uppladdning"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <div style={{ display: 'grid', gridTemplateColumns: compact ? '1fr' : '1fr 1fr 1fr', gap: '1rem' }}>
          <VariableField
            label="Verifikationsnummer-variabel"
            value={attachStep.voucherNumberVariable || ''}
            onChange={(value) => updateStep({ voucherNumberVariable: value })}
            placeholder="Välj variabel med verifikationsnummer"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />

          <VariableField
            label="Verifikationsserie-variabel (valfritt)"
            value={attachStep.voucherSeriesVariable || ''}
            onChange={(value) => updateStep({ voucherSeriesVariable: value })}
            placeholder="Välj variabel med verifikationsserie"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />

          <VariableField
            label="Resultat-variabel"
            value={attachStep.variableName || 'var-fortnox-attachment'}
            onChange={(value) => updateStep({ variableName: value })}
            placeholder="Variabelnamn för kopplingsresultat"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />
        </div>

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#fef3c7',
          borderRadius: '0.5rem',
          border: '1px solid #f59e0b'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>📎</span>
            <span style={{ fontWeight: '500', color: '#92400e' }}>Filkoppling till verifikation</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#92400e',
            lineHeight: '1.4'
          }}>
            Kopplar en redan uppladdad fil till en befintlig verifikation i Fortnox.
          </p>
        </div>
      </>
    )
  }

  const renderFortnoxUploadAndCreateVoucherFields = () => {
    const combinedStep = editedStep as FortnoxUploadAndCreateVoucherStep

    return (
      <>
        {/* Two-column layout with full width */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '2rem',
          marginBottom: '1.5rem',
          width: '100%'
        }}>
          {/* Left column: File Upload and Output */}
          <div style={{ width: '100%' }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              color: '#374151',
              fontSize: compact ? '0.875rem' : '1rem',
              paddingBottom: '0.5rem',
              borderBottom: '2px solid #e5e7eb'
            }}>
              📤 Filuppladdning
            </h4>

            <VariableField
              label="Fil-variabel"
              value={combinedStep.fileInputVariable || ''}
              onChange={(value) => updateStep({ fileInputVariable: value })}
              placeholder="Välj variabel med base64-filinnehåll"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />

            <h4 style={{
              margin: '2rem 0 1rem 0',
              color: '#374151',
              fontSize: compact ? '0.875rem' : '1rem',
              paddingBottom: '0.5rem',
              borderBottom: '2px solid #e5e7eb'
            }}>
              📦 Output
            </h4>

            <VariableField
              label="Resultat-variabel"
              value={combinedStep.variableName || 'var-fortnox-voucher-with-file'}
              onChange={(value) => updateStep({ variableName: value })}
              placeholder="Variabelnamn för kombinerat resultat"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />
          </div>

          {/* Right column: Voucher Creation */}
          <div style={{ width: '100%' }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              color: '#374151',
              fontSize: compact ? '0.875rem' : '1rem',
              paddingBottom: '0.5rem',
              borderBottom: '2px solid #e5e7eb'
            }}>
              🧾 Verifikationsskapande
            </h4>

            <VariableField
              label="Verifikation input-variabel"
              value={combinedStep.voucherInputVariable || ''}
              onChange={(value) => updateStep({ voucherInputVariable: value })}
              placeholder="Välj variabel med data för verifikationen"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />

            <div style={{ marginTop: '1rem' }}>
              <TextAreaField
                label="AI-prompt"
                value={combinedStep.aiPrompt || ''}
                onChange={(value) => updateStep({ aiPrompt: value })}
                placeholder="Beskriv hur AI:n ska skapa verifikationsraderna"
                compact={compact}
                minHeight='150px'
              />
            </div>

            <div style={{
              display: 'grid',
              gridTemplateColumns: compact ? '1fr' : '1fr 1fr',
              gap: '1rem',
              marginTop: '1rem'
            }}>
              <TextAreaField
                label="Verifikationsbeskrivning (valfritt)"
                value={combinedStep.voucherDescription || ''}
                onChange={(value) => updateStep({ voucherDescription: value })}
                placeholder="Beskrivning av verifikationen"
                compact={compact}
              />

              <SelectField
                label="Verifikationsserie"
                value={combinedStep.voucherSeries || 'A'}
                onChange={(value) => updateStep({ voucherSeries: value })}
                options={[
                  { value: 'A', label: 'A - Allmän' },
                  { value: 'B', label: 'B - Bank' },
                  { value: 'C', label: 'C - Kassa' },
                  { value: 'D', label: 'D - Dagsrapporter' },
                  { value: 'Z', label: 'Z - Övrigt' }
                ]}
                compact={compact}
              />
            </div>
          </div>
        </div>

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#f0f9ff',
          borderRadius: '0.5rem',
          border: '1px solid #0ea5e9'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>📋</span>
            <span style={{ fontWeight: '500', color: '#0c4a6e' }}>Kombinerad operation</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#0c4a6e',
            lineHeight: '1.4'
          }}>
            Laddar upp filen till Fortnox arkiv, skapar en verifikation med AI och kopplar automatiskt filen till verifikationen.
          </p>
        </div>
      </>
    )
  }

  // eAccounting render functions
  const renderEAccountingUploadFileFields = () => {
    const uploadStep = editedStep as EAccountingUploadFileStep

    return (
      <>
        <VariableField
          label="Input-variabel"
          value={uploadStep.inputVariable || ''}
          onChange={(value) => updateStep({ inputVariable: value })}
          placeholder="Välj variabel med base64-filinnehåll"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <div style={{ display: 'grid', gridTemplateColumns: compact ? '1fr' : '1fr 1fr', gap: '1rem' }}>
          <VariableField
            label="Filnamn (valfritt)"
            value={uploadStep.filename || ''}
            onChange={(value) => updateStep({ filename: value })}
            placeholder="dokument.pdf eller ${variabel}"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />

          <SelectField
            label="Content Type"
            value={uploadStep.contentType || ''}
            onChange={(value) => updateStep({ contentType: value })}
            options={[
              { value: '', label: 'Auto-detektera från filnamn' },
              { value: 'application/pdf', label: 'PDF' },
              { value: 'image/jpeg', label: 'JPEG' },
              { value: 'image/png', label: 'PNG' },
              { value: 'image/tiff', label: 'TIFF' }
            ]}
            compact={compact}
          />
        </div>

        <TextAreaField
          label="Kommentar (valfritt)"
          value={uploadStep.comment || ''}
          onChange={(value) => updateStep({ comment: value })}
          placeholder="Beskrivning av filen"
          compact={compact}
        />

        <VariableField
          label="Variabelnamn för resultat"
          value={uploadStep.variableName || ''}
          onChange={(value) => updateStep({ variableName: value })}
          placeholder="var-eaccounting-file"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#f0fdf4',
          borderRadius: '0.5rem',
          border: '1px solid #22c55e'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>📤</span>
            <span style={{ fontWeight: '500', color: '#15803d' }}>Filuppladdning till eAccounting</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#15803d',
            lineHeight: '1.4'
          }}>
            Filen kommer att laddas upp till eAccounting Attachments och returnera ett attachment-ID som kan användas för att koppla till verifikationer.
          </p>
        </div>
      </>
    )
  }

  const renderEAccountingCreateVoucherFields = () => {
    const voucherStep = editedStep as EAccountingCreateVoucherStep

    return (
      <>
        <VariableField
          label="Input-variabel"
          value={voucherStep.inputVariable || ''}
          onChange={(value) => updateStep({ inputVariable: value })}
          placeholder="Välj variabel med data för verifikationen"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <TextAreaField
          label="AI-prompt"
          value={voucherStep.aiPrompt || ''}
          onChange={(value) => updateStep({ aiPrompt: value })}
          placeholder="Beskriv hur AI:n ska skapa verifikationsraderna, t.ex. 'Skapa en inköpsverifikation med moms'"
          compact={compact}
        />

        <div style={{ display: 'grid', gridTemplateColumns: compact ? '1fr' : '1fr 1fr 1fr', gap: '1rem' }}>
          <TextAreaField
            label="Verifikationstext (valfritt)"
            value={voucherStep.voucherText || ''}
            onChange={(value) => updateStep({ voucherText: value })}
            placeholder="Beskrivning av verifikationen"
            compact={compact}
          />

          <VariableField
            label="Verifikationsdatum (valfritt)"
            value={voucherStep.voucherDate || ''}
            onChange={(value) => updateStep({ voucherDate: value })}
            placeholder="YYYY-MM-DD eller välj variabel"
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />

          <VariableField
            label="Nummerserie (valfritt)"
            value={voucherStep.numberSeries || ''}
            onChange={(value) => updateStep({ numberSeries: value })}
            placeholder="A, B, C etc."
            steps={steps}
            currentStepIndex={currentStepIndex}
            compact={compact}
          />
        </div>

        <VariableField
          label="Variabelnamn för resultat"
          value={voucherStep.variableName || ''}
          onChange={(value) => updateStep({ variableName: value })}
          placeholder="var-eaccounting-voucher"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#f0f9ff',
          borderRadius: '0.5rem',
          border: '1px solid #0ea5e9'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>🤖</span>
            <span style={{ fontWeight: '500', color: '#0369a1' }}>AI-driven verifikationsskapande</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#0369a1',
            lineHeight: '1.4'
          }}>
            AI:n kommer att analysera input-data och skapa balanserade verifikationsrader automatiskt baserat på din prompt och eAccounting kontoplan.
          </p>
        </div>
      </>
    )
  }

  const renderEAccountingAttachFileToVoucherFields = () => {
    const attachStep = editedStep as EAccountingAttachFileToVoucherStep

    return (
      <>
        <VariableField
          label="Attachment ID-variabel"
          value={attachStep.attachmentIdVariable || ''}
          onChange={(value) => updateStep({ attachmentIdVariable: value })}
          placeholder="Välj variabel med attachment ID från filuppladdning"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <VariableField
          label="Voucher ID-variabel"
          value={attachStep.voucherIdVariable || ''}
          onChange={(value) => updateStep({ voucherIdVariable: value })}
          placeholder="Välj variabel med voucher ID"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <VariableField
          label="Variabelnamn för resultat"
          value={attachStep.variableName || ''}
          onChange={(value) => updateStep({ variableName: value })}
          placeholder="var-eaccounting-attachment"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#fef3c7',
          borderRadius: '0.5rem',
          border: '1px solid #f59e0b'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>📎</span>
            <span style={{ fontWeight: '500', color: '#92400e' }}>Filkoppling till verifikation</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#92400e',
            lineHeight: '1.4'
          }}>
            Kopplar en redan uppladdad fil till en befintlig verifikation i eAccounting.
          </p>
        </div>
      </>
    )
  }

  const renderEAccountingUploadAndCreateVoucherFields = () => {
    const combinedStep = editedStep as EAccountingUploadAndCreateVoucherStep

    return (
      <>
        {/* Two-column layout with full width */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '2rem',
          marginBottom: '1.5rem',
          width: '100%'
        }}>
          {/* Left column: File Upload */}
          <div style={{ width: '100%' }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              color: '#374151',
              fontSize: compact ? '0.875rem' : '1rem',
              paddingBottom: '0.5rem',
              borderBottom: '2px solid #e5e7eb'
            }}>
              📤 Filuppladdning
            </h4>

            <VariableField
              label="Fil-variabel"
              value={combinedStep.fileInputVariable || ''}
              onChange={(value) => updateStep({ fileInputVariable: value })}
              placeholder="Välj variabel med base64-filinnehåll"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />

            <VariableField
              label="Filnamn (valfritt)"
              value={combinedStep.filename || ''}
              onChange={(value) => updateStep({ filename: value })}
              placeholder="dokument.pdf eller ${variabel}"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />

            <SelectField
              label="Content Type"
              value={combinedStep.contentType || ''}
              onChange={(value) => updateStep({ contentType: value })}
              options={[
                { value: '', label: 'Auto-detektera från filnamn' },
                { value: 'application/pdf', label: 'PDF' },
                { value: 'image/jpeg', label: 'JPEG' },
                { value: 'image/png', label: 'PNG' },
                { value: 'image/tiff', label: 'TIFF' }
              ]}
              compact={compact}
            />

            <TextAreaField
              label="Filkommentar (valfritt)"
              value={combinedStep.fileComment || ''}
              onChange={(value) => updateStep({ fileComment: value })}
              placeholder="Beskrivning av filen"
              compact={compact}
            />
          </div>

          {/* Right column: Voucher Creation */}
          <div style={{ width: '100%' }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              color: '#374151',
              fontSize: compact ? '0.875rem' : '1rem',
              paddingBottom: '0.5rem',
              borderBottom: '2px solid #e5e7eb'
            }}>
              🧾 Verifikationsskapande
            </h4>

            <VariableField
              label="Verifikation input-variabel"
              value={combinedStep.voucherInputVariable || ''}
              onChange={(value) => updateStep({ voucherInputVariable: value })}
              placeholder="Välj variabel med data för verifikationen"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />

            <TextAreaField
              label="AI-prompt"
              value={combinedStep.aiPrompt || ''}
              onChange={(value) => updateStep({ aiPrompt: value })}
              placeholder="Beskriv hur AI:n ska skapa verifikationsraderna"
              compact={compact}
            />

            <VariableField
              label="Verifikationstext (valfritt)"
              value={combinedStep.voucherText || ''}
              onChange={(value) => updateStep({ voucherText: value })}
              placeholder="Beskrivning av verifikationen"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
            />

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              <VariableField
                label="Verifikationsdatum"
                value={combinedStep.voucherDate || ''}
                onChange={(value) => updateStep({ voucherDate: value })}
                placeholder="YYYY-MM-DD eller ${variabel}"
                steps={steps}
                currentStepIndex={currentStepIndex}
                compact={compact}
              />

              <VariableField
                label="Nummerserie"
                value={combinedStep.numberSeries || ''}
                onChange={(value) => updateStep({ numberSeries: value })}
                placeholder="A, B, C etc."
                steps={steps}
                currentStepIndex={currentStepIndex}
                compact={compact}
              />
            </div>
          </div>
        </div>

        <VariableField
          label="Variabelnamn för resultat"
          value={combinedStep.variableName || ''}
          onChange={(value) => updateStep({ variableName: value })}
          placeholder="var-eaccounting-voucher-with-file"
          steps={steps}
          currentStepIndex={currentStepIndex}
          compact={compact}
        />

        <div style={{
          marginTop: '1rem',
          padding: '1rem',
          backgroundColor: '#f3e8ff',
          borderRadius: '0.5rem',
          border: '1px solid #a855f7'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
            <span style={{ fontSize: '1.25rem', marginRight: '0.5rem' }}>🚀</span>
            <span style={{ fontWeight: '500', color: '#7c3aed' }}>Kombinerad operation</span>
          </div>
          <p style={{
            margin: 0,
            fontSize: compact ? '0.75rem' : '0.875rem',
            color: '#7c3aed',
            lineHeight: '1.4'
          }}>
            Detta steg laddar upp filen, skapar verifikationen med AI och kopplar automatiskt filen till verifikationen i en enda operation.
          </p>
        </div>
      </>
    )
  }

  const renderAPIFields = () => {
    switch (editedStep.type) {
      case 'fortnoxCreateVoucher':
        return renderFortnoxCreateVoucherFields()
      case 'fortnoxUploadFile':
        return renderFortnoxUploadFileFields()
      case 'fortnoxAttachFileToVoucher':
        return renderFortnoxAttachFileToVoucherFields()
      case 'fortnoxUploadAndCreateVoucher':
        return renderFortnoxUploadAndCreateVoucherFields()
      case 'eAccountingUploadFile':
        return renderEAccountingUploadFileFields()
      case 'eAccountingCreateVoucher':
        return renderEAccountingCreateVoucherFields()
      case 'eAccountingAttachFileToVoucher':
        return renderEAccountingAttachFileToVoucherFields()
      case 'eAccountingUploadAndCreateVoucher':
        return renderEAccountingUploadAndCreateVoucherFields()
      default:
        return (
          <div style={{
            padding: '2rem',
            textAlign: 'center',
            backgroundColor: '#f9fafb',
            borderRadius: '0.5rem',
            color: '#6b7280',
            fontSize: compact ? '0.875rem' : '1rem'
          }}>
            <div style={{ marginBottom: '0.5rem', fontSize: '2rem' }}>🚧</div>
            <div style={{ fontWeight: '500', marginBottom: '0.5rem' }}>API Steps Coming Soon</div>
            <div style={{ fontSize: '0.875rem' }}>
              API step editors will be implemented in future versions.
            </div>
          </div>
        )
    }
  }

  const getTitle = () => {
    switch (editedStep.type) {
      case 'fortnoxCreateVoucher':
        return compact ? 'Skapa Fortnox Verifikation' : 'Konfigurera Fortnox Verifikation'
      case 'fortnoxUploadFile':
        return compact ? 'Ladda upp fil till Fortnox' : 'Konfigurera Fortnox Filuppladdning'
      case 'fortnoxAttachFileToVoucher':
        return compact ? 'Koppla fil till verifikation' : 'Konfigurera Filkoppling'
      case 'fortnoxUploadAndCreateVoucher':
        return compact ? 'Ladda upp fil och skapa verifikation' : 'Konfigurera Kombinerad Operation'
      case 'eAccountingUploadFile':
        return compact ? 'Ladda upp fil till eAccounting' : 'Konfigurera eAccounting Filuppladdning'
      case 'eAccountingCreateVoucher':
        return compact ? 'Skapa eAccounting Verifikation' : 'Konfigurera eAccounting Verifikation'
      case 'eAccountingAttachFileToVoucher':
        return compact ? 'Koppla fil till verifikation' : 'Konfigurera eAccounting Filkoppling'
      case 'eAccountingUploadAndCreateVoucher':
        return compact ? 'Ladda upp fil och skapa verifikation' : 'Konfigurera eAccounting Kombinerad Operation'
      default:
        return compact ? 'Konfigurera API-steg' : 'Edit API Step'
    }
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderAPIFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}
