import { UniversalStepEditor } from './step-editors'
import { RpaStep } from '@rpa-project/shared'

interface StepEditorProps {
  step: RpaStep
  onSave: (step: RpaStep) => void
  onCancel: () => void
  compact?: boolean
  steps?: RpaStep[]
  currentStepIndex?: number
}

export function StepEditor({ step, onSave, onCancel, compact = false, steps = [], currentStepIndex = 0 }: StepEditorProps) {
  // Use the new universal step editor that automatically selects the right editor component
  return (
    <UniversalStepEditor
      step={step}
      onSave={onSave}
      onCancel={onCancel}
      compact={compact}
      steps={steps}
      currentStepIndex={currentStepIndex}
    />
  )
}
