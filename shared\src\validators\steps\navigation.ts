import { RpaStep, NavigateStep } from '../../types/steps';
import { ValidationResult, ValidationError, createStepFromType } from '../../utils';

export function validateNavigationStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'navigate':
      const navigateStep = step as NavigateStep;
      if (!navigateStep.url || navigateStep.url.trim() === '') {
        errors.push({
          field: 'url',
          message: 'URL is required for navigate step',

        });
      } else {
        try {
          new URL(navigateStep.url);
        } catch {
          errors.push({
            field: 'url',
            message: 'Invalid URL format',

          });
        }
      }
      break;

    case 'goBack':
    case 'goForward':
    case 'reload':
      // These steps don't have additional validation requirements
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function createNavigationStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'navigate':
    case 'goBack':
    case 'goForward':
    case 'reload':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not a navigation step type: ${stepType}`);
  }
}
