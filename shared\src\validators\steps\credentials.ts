import { RpaStep, FillPasswordStep, Fill2FAStep } from '../../types/steps';
import { ValidationResult, ValidationError, createStepFromType } from '../../utils';

export function validateCredentialStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'fillPassword':
    case 'fill2FA':
      const credStep = step as FillPasswordStep | Fill2FAStep;
      
      if (!credStep.selector || credStep.selector.trim() === '') {
        errors.push({
          field: 'selector',
          message: 'Selector is required',
          
        });
      }

      if (!credStep.credentialId || credStep.credentialId.trim() === '') {
        errors.push({
          field: 'credentialId',
          message: 'Credential ID is required',
          
        });
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function createCredentialStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'fillPassword':
    case 'fill2FA':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not a credential step type: ${stepType}`);
  }
}
