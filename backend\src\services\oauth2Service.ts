import axios from 'axios';
import { OAuth2Provider, OAuth2TokenResponse, CreateCustomerTokenRequest, generateId } from '@rpa-project/shared';
import { getOAuth2Config, validateOAuth2Config, generateAuthUrl, generateState, parseState } from '../config/oauth2';
import { customerService } from './customerService';

export class OAuth2Service {
  /**
   * Initiate OAuth2 flow for a customer
   */
  async initiateOAuth2Flow(customerId: string, provider: OAuth2Provider, tokenName: string, description?: string): Promise<string> {
    // Validate provider configuration
    if (!validateOAuth2Config(provider)) {
      throw new Error(`OAuth2 configuration incomplete for provider: ${provider}`);
    }

    // Check if customer exists
    const customer = await customerService.getCustomer(customerId);
    if (!customer) {
      throw new Error('Customer not found');
    }

    // Generate state parameter
    const state = generateState(customerId, tokenName);

    // Generate authorization URL
    const authUrl = generateAuthUrl(provider, state);

    return authUrl;
  }

  /**
   * Handle OAuth2 callback and exchange code for tokens
   */
  async handleOAuth2Callback(provider: OAuth2Provider, code: string, state: string): Promise<void> {
    const config = getOAuth2Config(provider);
    if (!config) {
      throw new Error(`OAuth2 configuration not found for provider: ${provider}`);
    }

    // Parse state parameter
    const { customerId, tokenName } = parseState(state);

    try {
      // Exchange authorization code for tokens
      const tokenResponse = await this.exchangeCodeForTokens(provider, code, config.redirectUri);

      // Calculate expiration time
      const expiresAt = new Date(Date.now() + (tokenResponse.expires_in * 1000));

      // Create customer token
      const createRequest: CreateCustomerTokenRequest = {
        name: tokenName,
        description: `OAuth2 token för ${provider}`,
        provider: provider,
        apiToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        expiresAt: expiresAt
      };

      await customerService.createCustomerToken(customerId, createRequest);

    } catch (error) {
      console.error('OAuth2 callback error:', error);
      throw new Error('Failed to exchange authorization code for tokens');
    }
  }

  /**
   * Refresh OAuth2 token
   */
  async refreshToken(tokenId: string): Promise<void> {
    // Get token data
    const tokenData = await customerService.getCustomerTokenData(tokenId);
    if (!tokenData || !tokenData.refreshToken) {
      throw new Error('Token or refresh token not found');
    }

    const token = await customerService.getCustomerToken(tokenId);
    if (!token) {
      throw new Error('Token not found');
    }

    const config = getOAuth2Config(token.provider);
    if (!config) {
      throw new Error(`OAuth2 configuration not found for provider: ${token.provider}`);
    }

    try {
      // Refresh the token
      const tokenResponse = await this.refreshOAuth2Token(token.provider, tokenData.refreshToken);

      // Calculate new expiration time
      const expiresAt = new Date(Date.now() + (tokenResponse.expires_in * 1000));

      // Update token
      await customerService.updateCustomerToken(tokenId, {
        apiToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token || tokenData.refreshToken,
        expiresAt: expiresAt
      });

    } catch (error) {
      console.error('Token refresh error:', error);
      throw new Error('Failed to refresh token');
    }
  }

  /**
   * Exchange authorization code for tokens
   */
  private async exchangeCodeForTokens(provider: OAuth2Provider, code: string, redirectUri: string): Promise<OAuth2TokenResponse> {
    const config = getOAuth2Config(provider);
    if (!config) {
      throw new Error(`OAuth2 configuration not found for provider: ${provider}`);
    }

    const data = new URLSearchParams({
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: redirectUri
    });

    const authHeader = Buffer.from(`${config.clientId}:${config.clientSecret}`).toString('base64');

    const response = await axios.post(config.tokenUrl, data, {
      headers: {
        'Authorization': `Basic ${authHeader}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    return response.data;
  }

  /**
   * Refresh OAuth2 token using refresh token
   */
  private async refreshOAuth2Token(provider: OAuth2Provider, refreshToken: string): Promise<OAuth2TokenResponse> {
    const config = getOAuth2Config(provider);
    if (!config) {
      throw new Error(`OAuth2 configuration not found for provider: ${provider}`);
    }

    const data = new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refreshToken
    });

    const authHeader = Buffer.from(`${config.clientId}:${config.clientSecret}`).toString('base64');

    const response = await axios.post(config.tokenUrl, data, {
      headers: {
        'Authorization': `Basic ${authHeader}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    return response.data;
  }

  /**
   * Check if tokens need refresh and refresh them automatically
   */
  async checkAndRefreshExpiredTokens(): Promise<void> {
    console.log('Checking for expired tokens...');

    try {
      // Get all OAuth2 tokens that expire within the next hour
      const expiringTokens = await this.getExpiringTokens();

      for (const token of expiringTokens) {
        try {
          console.log(`Refreshing token ${token.id} for customer ${token.customerId}`);
          await this.refreshToken(token.id);
          console.log(`Successfully refreshed token ${token.id}`);
        } catch (error) {
          console.error(`Failed to refresh token ${token.id}:`, error);
          // Continue with other tokens even if one fails
        }
      }

      console.log(`Checked ${expiringTokens.length} expiring tokens`);
    } catch (error) {
      console.error('Error checking expired tokens:', error);
    }
  }

  /**
   * Get tokens that are expiring within the next hour
   */
  private async getExpiringTokens(): Promise<Array<{ id: string; customerId: string; provider: OAuth2Provider }>> {
    const tokens = await customerService.getExpiringTokens(60);
    return tokens.map(token => ({
      id: token.id,
      customerId: token.customerId,
      provider: token.provider
    }));
  }

  /**
   * Check if a specific token is expired or expiring soon
   */
  async isTokenExpired(tokenId: string): Promise<boolean> {
    const token = await customerService.getCustomerToken(tokenId);
    if (!token || !token.expiresAt) {
      return false;
    }

    // Consider token expired if it expires within the next 5 minutes
    const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);
    return token.expiresAt <= fiveMinutesFromNow;
  }
}

// Export singleton instance
export const oauth2Service = new OAuth2Service();
