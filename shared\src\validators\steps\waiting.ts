import { RpaStep, WaitForSelectorStep, WaitForTimeoutStep } from '../../types/steps';
import { ValidationResult, ValidationError, createStepFromType } from '../../utils';

export function validateWaitingStep(step: RpaStep): ValidationResult {
  const errors: ValidationError[] = [];

  switch (step.type) {
    case 'waitForSelector':
      const selectorStep = step as WaitForSelectorStep;
      if (!selectorStep.selector || selectorStep.selector.trim() === '') {
        errors.push({
          field: 'selector',
          message: 'Selector is required',
          
        });
      }
      break;

    case 'waitForTimeout':
      const timeoutStep = step as WaitForTimeoutStep;
      if (!timeoutStep.duration || timeoutStep.duration <= 0) {
        errors.push({
          field: 'duration',
          message: 'Duration must be greater than 0',
          
        });
      }
      break;

    case 'waitForUrl':
      // URL validation could be added here if needed
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

export function createWaitingStepFromType(stepType: string): RpaStep {
  switch (stepType) {
    case 'waitForSelector':
    case 'waitForTimeout':
    case 'waitForUrl':
      return createStepFromType(stepType);
    default:
      throw new Error(`Not a waiting step type: ${stepType}`);
  }
}
