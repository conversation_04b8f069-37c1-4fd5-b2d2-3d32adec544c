import { useState, useEffect, useRef, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { Customer } from '@rpa-project/shared'
import { customerApi } from '../services/api'
import { CustomerForm } from '../components/customers/CustomerForm'

export function Customers() {
  const navigate = useNavigate()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [offset, setOffset] = useState(0)
  const [searchQuery, setSearchQuery] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)

  const ITEMS_PER_PAGE = 20

  useEffect(() => {
    loadCustomers(true)
  }, [])

  // No need to reload when search query changes since we use client-side filtering
  // useEffect(() => {
  //   if (searchQuery) {
  //     // For search, we use client-side filtering
  //     // No need to reload from server
  //   } else {
  //     // When clearing search, reload from server
  //     loadCustomers(true)
  //   }
  // }, [searchQuery])

  // Filter customers based on search query
  const filteredCustomers = customers.filter(customer => {
    if (!searchQuery.trim()) return true

    const query = searchQuery.toLowerCase()

    // Search in customer name
    if (customer.name.toLowerCase().includes(query)) return true

    // Search in customer number
    if (customer.customerNumber.toLowerCase().includes(query)) return true

    // Search in Visma number
    if (customer.vismaNumber?.toLowerCase().includes(query)) return true

    return false
  })

  // Intersection Observer for infinite scroll
  const lastCustomerElementRefCallback = useCallback((node: HTMLTableRowElement | null) => {
    if (loading || loadingMore) return
    if (observerRef.current) observerRef.current.disconnect()

    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadCustomers(false)
      }
    })

    if (node) observerRef.current.observe(node)
  }, [loading, loadingMore, hasMore])

  // Cleanup observer on unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [])

  const loadCustomers = async (reset = false) => {
    try {
      if (reset) {
        setLoading(true)
        setOffset(0)
      } else {
        setLoadingMore(true)
      }

      const currentOffset = reset ? 0 : offset
      const response = await customerApi.getCustomers({
        limit: ITEMS_PER_PAGE,
        offset: currentOffset
        // Removed search parameter - using client-side filtering instead
      })

      if (response.success && response.data) {
        const newCustomers = response.data

        if (reset) {
          setCustomers(newCustomers)
        } else {
          setCustomers(prev => [...prev, ...newCustomers])
        }

        // Check if we have more data
        setHasMore(newCustomers.length === ITEMS_PER_PAGE)
        setOffset(currentOffset + newCustomers.length)
        setError(null)
      } else {
        setError(response.error || 'Misslyckades att ladda kunder')
      }
    } catch (err) {
      setError('Misslyckades att ladda kunder')
      console.error('Error loading customers:', err)
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }

  const handleCreateCustomer = () => {
    setEditingCustomer(null)
    setShowForm(true)
  }

  const handleEditCustomer = (customer: Customer) => {
    navigate(`/customers/${customer.id}`)
  }

  const handleDeleteCustomer = async (id: string) => {
    if (!confirm('Är du säker på att du vill ta bort denna kund?')) {
      return
    }

    try {
      const response = await customerApi.deleteCustomer(id)
      if (response.success) {
        await loadCustomers(true)
      } else {
        setError(response.error || 'Misslyckades att ta bort kund')
      }
    } catch (err) {
      setError('Misslyckades att ta bort kund')
      console.error('Error deleting customer:', err)
    }
  }

  const handleFormSuccess = (customerId?: string) => {
    setShowForm(false)
    setEditingCustomer(null)

    if (customerId) {
      // Navigate to the newly created customer's detail page
      navigate(`/customers/${customerId}`)
    } else {
      // For updates, just reload the customers list
      loadCustomers(true)
    }
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingCustomer(null)
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('sv-SE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-text">Laddar kunder...</div>
      </div>
    )
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-header-content">
          <p className="dashboard-title">Kunder</p>
          <p className="dashboard-subtitle">
            Hantera kundinformation och kundnummer.
          </p>
        </div>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <div style={{ position: 'relative' }}>
            <input
              type="text"
              placeholder="🔎 Sök kunder, kundnummer..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                style={{
                  position: 'absolute',
                  right: '0.5rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  color: '#6b7280',
                  fontSize: '1.25rem'
                }}
                title="Rensa sökning"
              >
                ×
              </button>
            )}
          </div>
          <button
            onClick={handleCreateCustomer}
            className="action-button primary"
          >
            <span>Skapa kund</span>
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-card">
          <h3 className="error-title">Fel vid laddning av kunder</h3>
          <p className="error-message">{error}</p>
        </div>
      )}

      {/* Customers List */}
      {customers.length === 0 && !loading ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">👥</div>
            <p className="empty-state-title">
              {searchQuery ? 'Inga kunder hittades' : 'Inga kunder än'}
            </p>
            <p className="empty-state-subtitle">
              {searchQuery
                ? 'Prova att ändra din sökning eller rensa sökfältet'
                : 'Skapa din första kund för att komma igång med kundhantering'
              }
            </p>
            {searchQuery ? (
              <button
                onClick={() => setSearchQuery('')}
                className="action-button secondary centered"
              >
                <span>Rensa sökning</span>
              </button>
            ) : (
              <button
                onClick={handleCreateCustomer}
                className="action-button secondary centered"
              >
                <span>Skapa första kund</span>
              </button>
            )}
          </div>
        </div>
      ) : (
        <>
          <h2 className="section-title">
            {searchQuery
              ? `Sökresultat (${filteredCustomers.length} av ${customers.length})`
              : `Dina kunder (${customers.length}${hasMore ? '+' : ''})`
            }
          </h2>
          <div className="table-container">
            <div className="activity-table">
              <table className="table">
                <thead>
                  <tr>
                    <th>Kund</th>
                    <th>Visma-nummer</th>
                    <th>Skapad</th>
                    <th>Åtgärder</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredCustomers.map((customer, index) => (
                    <tr
                      key={customer.id}
                      ref={index === filteredCustomers.length - 1 ? lastCustomerElementRefCallback : null}
                    >
                      <td>
                        <div style={{ cursor: 'pointer' }} onClick={() => handleEditCustomer(customer)}>
                          <div className="flow-name">{customer.name}</div>
                          <div className="flow-description-small">{customer.customerNumber}</div>
                        </div>
                      </td>
                      <td className="secondary-text">
                        {customer.vismaNumber || '—'}
                      </td>
                      <td className="secondary-text">
                        {formatDate(customer.createdAt)}
                      </td>
                      <td>
                        <div className="flow-actions">
                          <button
                            onClick={() => handleEditCustomer(customer)}
                            className="action-button-small icon-only"
                            title="Redigera kund"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                          </button>
                          <button
                            onClick={() => handleDeleteCustomer(customer.id)}
                            className="action-button-small danger"
                            title="Ta bort kund"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <polyline points="3,6 5,6 21,6"></polyline>
                              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                              <line x1="10" y1="11" x2="10" y2="17"></line>
                              <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Loading indicator for infinite scroll */}
          {loadingMore && (
            <div style={{ textAlign: 'center', padding: '1rem', color: '#6b7280' }}>
              Laddar fler kunder...
            </div>
          )}

          {/* End indicator */}
          {!hasMore && customers.length > 0 && (
            <div style={{ textAlign: 'center', padding: '1rem', color: '#6b7280' }}>
              {searchQuery ? 'Alla sökresultat visas' : 'Alla kunder har laddats'}
            </div>
          )}
        </>
      )}

      {/* Customer form modal */}
      {showForm && (
        <CustomerForm
          customer={editingCustomer}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      )}
    </div>
  )
}
