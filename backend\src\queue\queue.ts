import { Queue, Worker, Job } from 'bullmq';
import { createClient } from 'redis';
import { ExecuteFlowRequest } from '@rpa-project/shared';
import { FlowExecutionWorker } from './worker';

const QUEUE_NAME = process.env.QUEUE_NAME || 'rpa-flows';
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const REDIS_HOST = process.env.REDIS_HOST || 'localhost';
const REDIS_PORT = parseInt(process.env.REDIS_PORT || '6379');
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || undefined;

// Redis connection configuration for redis client
const redisClientConfig = {
  url: REDIS_URL
};

// Redis connection configuration for BullMQ
const redisConfig = {
  host: REDIS_HOST,
  port: REDIS_PORT,
  password: REDIS_PASSWORD,
  maxRetriesPerRequest: null,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
};

// Global queue and worker instances
let flowQueue: Queue;
let flowWorker: Worker;
let redisClient: any;

export async function initializeQueue(): Promise<void> {
  try {
    // Initialize Redis client
    redisClient = createClient(redisClientConfig);
    
    redisClient.on('error', (err: any) => {
      console.error('Redis Client Error:', err);
    });

    redisClient.on('connect', () => {
      console.log('✅ Connected to Redis');
    });

    await redisClient.connect();

    // Initialize BullMQ queue
    flowQueue = new Queue(QUEUE_NAME, {
      connection: redisConfig,
      defaultJobOptions: {
        removeOnComplete: 100, // Keep last 100 completed jobs
        removeOnFail: 50,      // Keep last 50 failed jobs
        attempts: 3,           // Retry failed jobs 3 times
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    // Initialize worker
    const concurrency = parseInt(process.env.QUEUE_CONCURRENCY || '5');
    flowWorker = new Worker(
      QUEUE_NAME,
      async (job: Job) => {
        const worker = new FlowExecutionWorker();
        return worker.processJob(job);
      },
      {
        connection: redisConfig,
        concurrency,
      }
    );

    // Worker event handlers
    flowWorker.on('completed', (job) => {
      console.log(`✅ Job ${job.id} completed successfully`);
    });

    flowWorker.on('failed', (job, err) => {
      console.error(`❌ Job ${job?.id} failed:`, err.message);
    });

    flowWorker.on('error', (err) => {
      console.error('Worker error:', err);
    });

    console.log(`🔄 Queue initialized with ${concurrency} workers`);
  } catch (error) {
    console.error('Failed to initialize queue:', error);
    throw error;
  }
}

export function getQueue(): Queue {
  if (!flowQueue) {
    throw new Error('Queue not initialized. Call initializeQueue() first.');
  }
  return flowQueue;
}

export function getWorker(): Worker {
  if (!flowWorker) {
    throw new Error('Worker not initialized. Call initializeQueue() first.');
  }
  return flowWorker;
}

export function getRedisClient(): any {
  if (!redisClient) {
    throw new Error('Redis client not initialized. Call initializeQueue() first.');
  }
  return redisClient;
}

export async function closeQueue(): Promise<void> {
  if (flowWorker) {
    await flowWorker.close();
  }
  if (flowQueue) {
    await flowQueue.close();
  }
  if (redisClient) {
    await redisClient.quit();
  }
}
