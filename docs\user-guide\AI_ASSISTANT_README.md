# AI-assistent för RPA-flödesgenerering

## Översikt

AI-assistenten är implementerad som ett backend-API med flexibel LLM provider-arkitektur som stöder både OpenAI och Azure OpenAI för att generera, optimera och felsöka RPA-flöden baserat på naturligt språk.

## Konfiguration

### LLM Provider-arkitektur

Systemet stöder enkelt byte mellan olika LLM-providers och modeller via miljövariabler:

```bash
# I backend/.env-fil

# LLM Provider Configuration
LLM_PROVIDER=openai                   # 'openai' | 'azure'
LLM_DEFAULT_MODEL=gpt-4o-mini        # Default model for all AI calls
LLM_FALLBACK_MODEL=gpt-4o-mini       # Fallback if default fails

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Azure OpenAI Configuration (optional)
AZURE_OPENAI_API_KEY=your_azure_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_MODEL_GPT4O_MINI=gpt-4o-mini-deployment
AZURE_MODEL_O1_MINI=o1-mini-deployment

# Server Configuration
PORT=3002
NODE_ENV=development
```

### Stödda Providers och Modeller

#### OpenAI Provider
- **gpt-4o-mini**: Snabb och kostnadseffektiv (standard)
- **o1-mini**: Avancerad reasoning (när tillgänglig)

#### Azure OpenAI Provider
- **gpt-4o-mini**: Via Azure deployment
- Anpassningsbara deployment-namn via miljövariabler

### Enkelt Provider-byte

```bash
# Byta från OpenAI till Azure
LLM_PROVIDER=azure

# Byta modell
LLM_DEFAULT_MODEL=o1-mini

# Byta tillbaka till OpenAI
LLM_PROVIDER=openai
```

API:et är automatiskt tillgängligt på `/api/ai-assistant` när servern startas.

### Frontend

AI-servicen är tillgänglig via:
```typescript
import { aiService } from './services/ai'
// eller
import { aiService } from './services/api'
```

## API Endpoints

### 1. Generera komplett flöde
**POST** `/api/ai-assistant/generate-flow`

Skapar ett helt nytt RPA-flöde från en textbeskrivning.

```typescript
// Request
{
  "prompt": "Skapa ett flöde som loggar in på Gmail, söker efter mail från <EMAIL> och laddar ner alla bilagor",
  "flowName": "Gmail bilagor", // optional
  "description": "Automatisk nedladdning av bilagor" // optional
}

// Response
{
  "success": true,
  "data": {
    "id": "flow_123",
    "name": "Gmail bilagor",
    "description": "Automatisk nedladdning av bilagor",
    "steps": [
      {
        "id": "step_1",
        "type": "navigate",
        "url": "https://gmail.com",
        "timeout": 30000
      },
      // ... fler steg
    ],
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Genererade 8 steg för flödet"
}
```

### 2. Föreslå nästa steg
**POST** `/api/ai-assistant/suggest-next-step`

Föreslår nästa steg för ett befintligt flöde.

```typescript
// Request
{
  "flowId": "flow_123", // optional
  "currentSteps": [
    {
      "id": "step_1",
      "type": "navigate",
      "url": "https://example.com"
    }
  ],
  "prompt": "Vad bör jag göra efter att ha loggat in?"
}

// Response
{
  "success": true,
  "data": [
    {
      "id": "step_2",
      "type": "waitForSelector",
      "selector": ".dashboard",
      "timeout": 30000
    }
  ],
  "message": "Föreslår 1 nya steg"
}
```

### 3. Optimera flöde
**POST** `/api/ai-assistant/optimize-flow`

Optimerar ett befintligt flöde för hastighet, tillförlitlighet eller underhållbarhet.

```typescript
// Request
{
  "flowId": "flow_123",
  "currentSteps": [...],
  "optimizationGoal": "reliability" // 'speed' | 'reliability' | 'maintainability' | 'general'
}

// Response
{
  "success": true,
  "data": [...], // optimerade steg
  "message": "Optimerade flödet för reliability"
}
```

### 4. Felsök flöde
**POST** `/api/ai-assistant/debug-flow`

Analyserar problem och föreslår korrigeringar.

```typescript
// Request
{
  "flowId": "flow_123",
  "currentSteps": [...],
  "errorDescription": "Flödet fastnar på inloggningssidan",
  "executionLogs": [ // optional
    {
      "level": "error",
      "message": "Element not found: #login-button"
    }
  ]
}

// Response
{
  "success": true,
  "data": {
    "steps": [...], // korrigerade steg
    "explanation": "Problemet verkar vara att login-knappen har en annan selektor..."
  },
  "message": "Analyserade problemet och föreslår 5 korrigerade steg"
}
```

### 5. Hälsokontroll
**GET** `/api/ai-assistant/health`

Kontrollerar AI-assistentens status och konfiguration för den aktiva LLM-providern.

```typescript
// Response
{
  "success": true,
  "data": {
    "aiStatus": "connected", // 'connected' | 'error'
    "provider": "openai",    // 'openai' | 'azure'
    "hasApiKey": true,
    "error": null,           // Felmeddelande vid problem
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "message": "AI Assistant health check"
}

// Vid fel (t.ex. ogiltig API-nyckel)
{
  "success": true,
  "data": {
    "aiStatus": "error",
    "provider": "openai",
    "hasApiKey": false,
    "error": "Invalid API key",
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "message": "AI Assistant health check"
}
```

## Frontend-användning

### Grundläggande användning

```typescript
import { aiService } from './services/ai'

// Generera ett nytt flöde
try {
  const flow = await aiService.generateFlow({
    prompt: "Logga in på Facebook och posta en status",
    flowName: "Facebook status"
  })
  console.log('Genererat flöde:', flow)
} catch (error) {
  console.error('Fel:', error.message)
}

// Föreslå nästa steg
const suggestions = await aiService.suggestNextStep({
  currentSteps: existingSteps,
  prompt: "Lägg till validering av inloggning"
})
```

### Säker användning med felhantering

```typescript
import { aiHelpers } from './services/ai'

// Kontrollera om AI är konfigurerat
const isReady = await aiHelpers.isConfigured()
if (!isReady) {
  console.log('AI assistant är inte konfigurerat')
  return
}

// Generera flöde med säker felhantering
const result = await aiHelpers.generateFlowSafely(
  "Skapa ett flöde för att skicka email",
  { flowName: "Email automation" }
)

if (result.success) {
  console.log('Flöde skapat:', result.flow)
} else {
  console.error('Fel:', result.error)
}
```

## Steg-typer som AI:n kan generera

AI-assistenten kan generera alla tillgängliga RPA-steg:

- **Navigation**: navigate, goBack, goForward, reload
- **Interaktion**: click, fill, type, selectOption, check, uncheck
- **Väntan**: waitForSelector, waitForTimeout, waitForUrl
- **Extraktion**: extractText, extractAttribute
- **Dokumentation**: takeScreenshot
- **Villkorlig logik**: conditionalClick, ifElementExists
- **Säkerhet**: fillPassword, fill2FA
- **AI-bearbetning**: extractPdfValues

## Felhantering

API:et inkluderar omfattande felhantering för alla LLM-providers:

- **Validering**: Alla inkommande data valideras med Joi
- **Provider-fel**: API-nyckelproblem, rate limits, nätverksfel för alla providers
- **Fallback-mekanismer**: Automatisk fallback till backup-modell vid fel
- **Parsing-fel**: Ogiltiga AI-svar hanteras gracefully
- **Steg-validering**: Genererade steg valideras mot RPA-schemat
- **Provider-switching**: Robust hantering vid byte mellan providers

## Säkerhet

- API-nycklar lagras säkert i miljövariabler
- Ingen känslig data loggas
- Input-validering förhindrar injection-attacker
- Rate limiting rekommenderas för produktionsmiljö

## Exempel på prompts

### Bra prompts:
- "Skapa ett flöde som loggar in på Gmail och läser olästa meddelanden"
- "Automatisera processen att fylla i en kontaktformulär på en webbsida"
- "Skapa ett flöde som laddar ner alla PDF-filer från en mapp på Google Drive"

### Undvik:
- För vaga beskrivningar: "gör något med email"
- Olagliga aktiviteter: "hacka in på..."
- Omöjliga uppgifter: "läs tankar från användare"

## Utveckling och testning

För att testa AI-assistenten lokalt:

1. Sätt upp OpenAI API-nyckel
2. Starta backend-servern
3. Använd health-endpoint för att verifiera konfiguration
4. Testa med enkla prompts först

```bash
# Testa health endpoint
curl http://localhost:3002/api/ai-assistant/health

# Testa flödesgenerering (PowerShell)
Invoke-RestMethod -Uri "http://localhost:3002/api/ai-assistant/generate-flow" -Method POST -ContentType "application/json" -Body '{"prompt": "Navigera till Google och sök efter RPA"}'
```

## ✅ Testresultat

AI-assistenten har testats och fungerar korrekt:

### Test 1: Enkel flödesgenerering
**Prompt**: "Navigera till Google och sök efter RPA"
**Resultat**: Genererade 9 steg inklusive:
- Navigate till Google
- Vänta på sökrutan
- Fyll i sökterm
- Klicka på sökknapp
- Vänta på resultat
- Ta skärmdump

### Test 2: Komplex flödesgenerering
**Prompt**: "Skapa ett flöde som loggar in på Gmail, söker efter mail från <EMAIL> och laddar ner alla bilagor"
**Resultat**: Genererade komplett flöde med inloggning, sökning och filhantering

### Test 3: Stegförslag
**Input**: Befintligt steg (navigate till Gmail)
**Prompt**: "Lägg till inloggning med användarnamn och lösenord"
**Resultat**: Föreslår korrekta inloggningssteg med waitForSelector, fill och click

Alla genererade steg valideras mot RPA-schemat och är redo att användas direkt i frontend.

## 🎨 AI-assistent Panel i FlowEditor

AI-assistenten är nu tillgänglig som en fast panel på höger sida av FlowEditor:

### Funktioner:
- **Fast panel** på höger sida (250px bred)
- **Toggle-knapp** (🤖) i FlowEditor header för att visa/dölja panelen
- **4 olika lägen**:
  - ✨ **Generera Flöde**: Skapa helt nytt flöde från beskrivning
  - 💡 **Föreslå Steg**: Lägg till steg till befintligt flöde
  - ⚡ **Optimera Flöde**: Förbättra prestanda och tillförlitlighet
  - 🔧 **Felsök Flöde**: Analysera och korrigera problem

### UI-funktioner:
- **Exempel-prompts** för varje läge
- **Realtids-feedback** med framstegsindikatorer
- **Flödesstatistik** som visar antal steg och senaste ändring
- **Felhantering** med tydliga felmeddelanden
- **Responsiv design** som matchar resten av applikationen

### Användning:
1. Öppna FlowEditor (skapa nytt eller redigera befintligt flöde)
2. AI-panelen visas automatiskt på höger sida
3. Välj läge (Generera/Föreslå/Optimera/Felsök)
4. Skriv din prompt eller välj ett exempel
5. Klicka på knappen för att köra AI-funktionen
6. Resultatet läggs automatiskt till i flödet

### Integration:
- **Automatisk steg-tillägg**: Nya steg läggs till i React Flow canvas
- **Flödes-ersättning**: Vid generering ersätts hela flödet
- **Steg-validering**: Alla AI-genererade steg valideras innan tillägg
- **Visuell feedback**: Steg placeras automatiskt i canvas med kopplingar
