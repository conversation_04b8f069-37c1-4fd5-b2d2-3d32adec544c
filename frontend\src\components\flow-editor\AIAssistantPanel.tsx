import { useState } from 'react'
import { RpaFlow, RpaStep } from '@rpa-project/shared'
import { aiService } from '../../services/ai'

interface AIAssistantPanelProps {
  flow: RpaFlow
  onFlowChange: (flow: RpaFlow) => void
  onAddSteps: (steps: RpaStep[]) => void
  selectedStep?: RpaStep | null
  onUpdateStep?: (stepId: string, updatedStep: RpaStep) => void
  onClearSelection?: () => void
}

interface ChatMessage {
  id: string
  text: string
  timestamp: Date
  type: 'user' | 'system'
}

export function AIAssistantPanel({
  flow,
  onFlowChange,
  onAddSteps,
  selectedStep,
  onUpdateStep,
  onClearSelection
}: AIAssistantPanelProps) {
  const [prompt, setPrompt] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([])
  const [, setLastResult] = useState<string | null>(null)

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value)

    // Auto-resize textarea
    const textarea = e.target
    textarea.style.height = 'auto'
    textarea.style.height = Math.min(textarea.scrollHeight, 400) + 'px'
  }

  const handleGenerateFlow = async () => {
    if (!prompt.trim()) {
      setError('Ange en beskrivning av flödet')
      return
    }

    // Add user message to chat history
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: prompt.trim(),
      timestamp: new Date(),
      type: 'user'
    }
    setChatHistory(prev => [...prev, userMessage])

    setIsLoading(true)
    setError(null)

    try {
      const generatedFlow = await aiService.generateFlow({
        prompt: prompt.trim(),
        flowName: flow.name,
        description: flow.description
      })

      // Replace current flow steps with generated ones
      onFlowChange({
        ...flow,
        steps: generatedFlow.steps,
        updatedAt: new Date()
      })

      // Add system response to chat history
      const systemMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `Genererade ${generatedFlow.steps.length} steg`,
        timestamp: new Date(),
        type: 'system'
      }
      setChatHistory(prev => [...prev, systemMessage])

      setLastResult(`Genererade ${generatedFlow.steps.length} steg`)
      setPrompt('')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Fel vid generering av flöde'
      setError(errorMessage)

      // Add error message to chat history
      const systemMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `Fel: ${errorMessage}`,
        timestamp: new Date(),
        type: 'system'
      }
      setChatHistory(prev => [...prev, systemMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddSteps = async () => {
    if (!prompt.trim()) {
      setError('Ange en beskrivning av stegen som ska läggas till')
      return
    }

    // Add user message to chat history
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: `Lägg till steg: ${prompt.trim()}`,
      timestamp: new Date(),
      type: 'user'
    }
    setChatHistory(prev => [...prev, userMessage])

    setIsLoading(true)
    setError(null)

    try {
      const suggestedSteps = await aiService.suggestNextStep({
        currentSteps: flow.steps,
        prompt: prompt.trim()
      })

      // Add the suggested steps to the flow
      onAddSteps(suggestedSteps)

      // Add system response to chat history
      const systemMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `Lade till ${suggestedSteps.length} steg`,
        timestamp: new Date(),
        type: 'system'
      }
      setChatHistory(prev => [...prev, systemMessage])

      setLastResult(`Lade till ${suggestedSteps.length} steg`)
      setPrompt('')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Fel vid tillägg av steg'
      setError(errorMessage)

      // Add error message to chat history
      const systemMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `Fel: ${errorMessage}`,
        timestamp: new Date(),
        type: 'system'
      }
      setChatHistory(prev => [...prev, systemMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditStep = async () => {
    if (!prompt.trim()) {
      setError('Ange instruktioner för hur steget ska redigeras')
      return
    }

    if (!selectedStep || !onUpdateStep) {
      setError('Ingen nod är vald för redigering')
      return
    }

    // Add user message to chat history
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: `Redigera steg: ${prompt.trim()}`,
      timestamp: new Date(),
      type: 'user'
    }
    setChatHistory(prev => [...prev, userMessage])

    setIsLoading(true)
    setError(null)

    try {
      const editedStep = await aiService.editStep({
        step: selectedStep,
        prompt: prompt.trim(),
        context: flow.steps.filter(step => step.id !== selectedStep.id).slice(-3) // Last 3 steps as context
      })

      // Update the step
      onUpdateStep(selectedStep.id, editedStep)

      // Clear selection
      if (onClearSelection) {
        onClearSelection()
      }

      // Add system response to chat history
      const systemMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `Redigerade steget: ${editedStep.type}`,
        timestamp: new Date(),
        type: 'system'
      }
      setChatHistory(prev => [...prev, systemMessage])

      setLastResult(`Redigerade steget: ${editedStep.type}`)
      setPrompt('')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Fel vid redigering av steg'
      setError(errorMessage)

      // Add error message to chat history
      const systemMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `Fel: ${errorMessage}`,
        timestamp: new Date(),
        type: 'system'
      }
      setChatHistory(prev => [...prev, systemMessage])
    } finally {
      setIsLoading(false)
    }
  }







  // Determine which action to take based on context
  const getActionButtons = () => {
    const buttons = []
    const hasSteps = flow.steps && flow.steps.length > 0

    // Show "Generera Flöde" button only when flow is empty
    if (!hasSteps) {
      buttons.push(
        <button
          key="generate"
          onClick={handleGenerateFlow}
          disabled={isLoading || !prompt.trim()}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: isLoading ? '#9ca3af' : '#10b981',
            color: 'white',
            border: 'none',
            borderRadius: '0.75rem',
            fontSize: '0.75rem',
            fontWeight: '600',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            transition: 'background-color 0.2s',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.5rem',
            whiteSpace: 'nowrap'
          }}
        >
          {isLoading ? (
            <>
              <span style={{
                width: '1rem',
                height: '1rem',
                border: '2px solid transparent',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
              Arbetar...
            </>
          ) : (
            'Lägg till steg'
          )}
        </button>
      )
    }

    // Show "Redigera vald nod" button if a step is selected
    if (selectedStep) {
      buttons.push(
        <button
          key="edit"
          onClick={handleEditStep}
          disabled={isLoading || !prompt.trim()}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: isLoading ? '#9ca3af' : '#f59e0b',
            color: 'white',
            border: 'none',
            borderRadius: '0.75rem',
            fontSize: '0.75rem',
            fontWeight: '600',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            transition: 'background-color 0.2s',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.5rem',
            whiteSpace: 'nowrap'
          }}
        >
          Redigera vald nod
        </button>
      )
    }
    // Show "Lägg till steg" button when flow has steps and no step is selected
    else if (hasSteps) {
      buttons.push(
        <button
          key="add"
          onClick={handleAddSteps}
          disabled={isLoading || !prompt.trim()}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: isLoading ? '#9ca3af' : '#10b981',
            color: 'white',
            border: 'none',
            borderRadius: '0.75rem',
            fontSize: '0.75rem',
            fontWeight: '600',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            transition: 'background-color 0.2s',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.5rem',
            whiteSpace: 'nowrap'
          }}
        >
          Lägg till steg
        </button>
      )
    }

    return buttons
  }



  return (
    <div style={{
      height: '100%',
      padding: '2rem 0.5rem 2rem 2rem',
      display: 'flex',
      flexDirection: 'column',
      gap: '0.75rem',
      backgroundColor: '#fbf9f8'
    }}>
      {/* Chat History */}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        gap: '0.5rem',
        overflowY: 'auto',
        padding: '1rem',
        backgroundColor: 'white',
        borderRadius: '1rem',
        border: '1px solid #e5e7eb',
        minHeight: '200px'
      }}>
        {chatHistory.length === 0 ? (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#9ca3af',
            fontSize: '0.75rem',
            textAlign: 'center'
          }}>
            Börja skicka promptar för att se din historik här
          </div>
        ) : (
          chatHistory.map((message) => (
            <div
              key={message.id}
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '0.25rem',
                padding: '0.5rem',
                borderRadius: '0.75rem',
                backgroundColor: message.type === 'user' ? '#eff6ff' : '#f0fdf4',
                border: `1px solid ${message.type === 'user' ? '#bfdbfe' : '#bbf7d0'}`,
                alignSelf: message.type === 'user' ? 'flex-end' : 'flex-start',
                maxWidth: '85%'
              }}
            >
              <div style={{
                fontSize: '0.625rem',
                color: '#6b7280',
                fontWeight: '500'
              }}>
                {message.type === 'user' ? 'Du' : 'Ebbot'} • {message.timestamp.toLocaleTimeString('sv-SE', { hour: '2-digit', minute: '2-digit' })}
              </div>
              <div style={{
                fontSize: '0.75rem',
                color: '#374151',
                lineHeight: '1.4',
                whiteSpace: 'pre-wrap'
              }}>
                {message.text}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Input Area */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem', flexShrink: 0 }}>
        

        {/* AI Thinking Indicator */}
        {isLoading && (
          <div style={{
            padding: '0.75rem',
            backgroundColor: '#f0f9ff',
            border: '1px solid #0ea5e9',
            borderRadius: '0.75rem',
            fontSize: '0.75rem',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            color: '#0369a1'
          }}>
            <div style={{
              display: 'flex',
              gap: '0.25rem'
            }}>
              <span className="thinking-dot" style={{ animationDelay: '0s' }}>•</span>
              <span className="thinking-dot" style={{ animationDelay: '0.2s' }}>•</span>
              <span className="thinking-dot" style={{ animationDelay: '0.4s' }}>•</span>
            </div>
            <span>Ebbot tänker</span>
          </div>
        )}

        {/* Selected Step Info */}
        {selectedStep && (
          <div style={{
            padding: '0.75rem',
            backgroundColor: '#fef3c7',
            border: '1px solid #f59e0b',
            borderRadius: '0.75rem',
            fontSize: '0.75rem',
            marginBottom: '0.75rem'
          }}>
            <div style={{ fontWeight: '600', color: '#92400e' }}>
              Vald nod: {selectedStep.type}
            </div>
            {selectedStep.description && (
              <div style={{ color: '#92400e', marginTop: '0.25rem' }}>
                {selectedStep.description}
              </div>
            )}
            <button
              onClick={onClearSelection}
              style={{
                marginTop: '0.5rem',
                padding: '0.25rem 0.5rem',
                backgroundColor: 'transparent',
                border: '1px solid #f59e0b',
                borderRadius: '1rem',
                fontSize: '0.625rem',
                color: '#92400e',
                cursor: 'pointer'
              }}
            >
              Rensa val
            </button>
          </div>
        )}

        {/* Textbox-like wrapper */}
        <div style={{
          border: '1px solid #e5e7eb',
          borderRadius: '1rem',
          backgroundColor: isLoading ? '#f9fafb' : 'white',
          padding: '0.75rem',
          display: 'flex',
          flexDirection: 'column',
          gap: '0.75rem'
        }}>
          <textarea
            value={prompt}
            onChange={handleTextareaChange}
            placeholder="Beskriv i text vad du skall hända. T.ex. 'Navigera till vismaonline.se' eller 'klicka på knappen bokför'"
            disabled={isLoading}
            style={{
              minHeight: '100px',
              maxHeight: '300px',
              border: 'none',
              outline: 'none',
              resize: 'vertical',
              fontSize: '0.75rem',
              fontFamily: 'inherit',
              backgroundColor: 'transparent',
              width: '100%'
            }}
          />

          {/* Action Buttons inside textbox */}
          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '0.5rem'
          }}>
            {getActionButtons()}
          </div>
        </div>

      </div>

      {/* Error Display */}
      {error && (
        <div style={{
          padding: '0.75rem',
          borderRadius: '0.75rem',
          fontSize: '0.75rem',
          flexShrink: 0,
          backgroundColor: '#fef2f2',
          border: '1px solid #fecaca',
          color: '#dc2626'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            {error}
          </div>
        </div>
      )}

      {/* Add animations */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          @keyframes thinking {
            0%, 60%, 100% {
              opacity: 0.3;
              transform: scale(1);
            }
            30% {
              opacity: 1;
              transform: scale(1.2);
            }
          }

          .thinking-dot {
            animation: thinking 1.4s infinite ease-in-out;
            font-size: 1.2em;
            line-height: 1;
          }
        `}
      </style>
    </div>
  )
}
