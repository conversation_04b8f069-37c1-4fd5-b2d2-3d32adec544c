import type { EAccountingUploadAndCreateVoucherStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { executeEAccountingUploadFile, EAccountingExecutorContext } from './eAccountingUploadFile';
import { executeEAccountingCreateVoucher } from './eAccountingCreateVoucher';
import { executeEAccountingAttachFileToVoucher } from './eAccountingAttachFileToVoucher';

/**
 * Execute eAccounting Upload and Create Voucher step
 * This is a combined operation that:
 * 1. Uploads a file to eAccounting Attachments
 * 2. Creates a voucher using AI processing
 * 3. Attaches the uploaded file to the created voucher
 */
export async function executeEAccountingUploadAndCreateVoucher(
  step: EAccountingUploadAndCreateVoucherStep,
  context: EAccountingExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing eAccounting Upload and Create Voucher: ${step.id}`,
      stepId: step.id
    });

    // STEP 1: Upload file to eAccounting
    onLog({
      level: 'info',
      message: 'Step 1: Uploading file to eAccounting Attachments',
      stepId: step.id
    });

    const uploadFileStep = {
      id: `${step.id}_upload`,
      type: 'eAccountingUploadFile' as const,
      name: 'Upload file for voucher',
      description: 'Upload file to eAccounting Attachments',
      inputVariable: step.fileInputVariable,
      filename: step.filename,
      contentType: step.contentType,
      comment: step.fileComment,
      variableName: 'temp_eaccounting_file_upload'
    };

    const uploadResult = await executeEAccountingUploadFile(uploadFileStep, context);

    if (!uploadResult.success) {
      onLog({
        level: 'error',
        message: `File upload failed: ${uploadResult.error}`,
        stepId: step.id
      });
      return uploadResult;
    }

    const uploadedFile = uploadResult.variables?.['temp_eaccounting_file_upload'];
    if (!uploadedFile || !uploadedFile.attachmentId) {
      throw new Error('File upload succeeded but no attachment ID was returned');
    }

    onLog({
      level: 'info',
      message: `File uploaded successfully. Attachment ID: ${uploadedFile.attachmentId}`,
      stepId: step.id
    });

    // STEP 2: Create voucher with AI processing
    onLog({
      level: 'info',
      message: 'Step 2: Creating voucher with AI processing',
      stepId: step.id
    });

    const createVoucherStep = {
      id: `${step.id}_voucher`,
      type: 'eAccountingCreateVoucher' as const,
      name: 'Create voucher with AI',
      description: 'Create voucher using AI processing',
      inputVariable: step.voucherInputVariable,
      aiPrompt: step.aiPrompt,
      voucherText: step.voucherText,
      voucherDate: step.voucherDate,
      numberSeries: step.numberSeries,
      variableName: 'temp_eaccounting_voucher_create'
    };

    const voucherResult = await executeEAccountingCreateVoucher(createVoucherStep, context);

    if (!voucherResult.success) {
      onLog({
        level: 'error',
        message: `Voucher creation failed: ${voucherResult.error}`,
        stepId: step.id
      });
      return voucherResult;
    }

    const createdVoucher = voucherResult.variables?.['temp_eaccounting_voucher_create'];
    if (!createdVoucher || !createdVoucher.voucherId) {
      throw new Error('Voucher creation succeeded but no voucher ID was returned');
    }

    onLog({
      level: 'info',
      message: `Voucher created successfully. ID: ${createdVoucher.voucherId}, Number: ${createdVoucher.voucherNumber}`,
      stepId: step.id
    });

    // STEP 3: Attach file to voucher
    onLog({
      level: 'info',
      message: 'Step 3: Attaching file to voucher',
      stepId: step.id
    });

    // Create temporary variables for the attachment step
    const tempContext = {
      ...context,
      variables: {
        ...variables,
        temp_attachment_id: uploadedFile.attachmentId,
        temp_voucher_id: createdVoucher.voucherId
      }
    };

    const attachFileStep = {
      id: `${step.id}_attach`,
      type: 'eAccountingAttachFileToVoucher' as const,
      name: 'Attach file to voucher',
      description: 'Attach uploaded file to created voucher',
      attachmentIdVariable: 'temp_attachment_id',
      voucherIdVariable: 'temp_voucher_id',
      variableName: 'temp_eaccounting_file_attachment'
    };

    const attachResult = await executeEAccountingAttachFileToVoucher(attachFileStep, tempContext);

    if (!attachResult.success) {
      onLog({
        level: 'error',
        message: `File attachment failed: ${attachResult.error}`,
        stepId: step.id
      });
      return attachResult;
    }

    onLog({
      level: 'info',
      message: `File ${uploadedFile.attachmentId} attached to voucher ${createdVoucher.voucherId} successfully`,
      stepId: step.id
    });

    // Store combined result in variables
    const variableName = step.variableName || getDefaultVariableName('eAccountingUploadAndCreateVoucher', stepIndex);
    const combinedResult = {
      // File information
      attachmentId: uploadedFile.attachmentId,
      filename: uploadedFile.filename,
      contentType: uploadedFile.contentType,
      fileComment: uploadedFile.comment,
      temporaryUrl: uploadedFile.temporaryUrl,
      // Voucher information
      voucherId: createdVoucher.voucherId,
      voucherNumber: createdVoucher.voucherNumber,
      voucherSeries: createdVoucher.voucherSeries,
      voucherDate: createdVoucher.voucherDate,
      voucherText: createdVoucher.voucherText,
      totalAmount: createdVoucher.totalAmount,
      rowsCount: createdVoucher.rowsCount,
      aiExplanation: createdVoucher.aiExplanation,
      // Attachment information
      documentId: attachResult.variables?.['temp_eaccounting_file_attachment']?.documentId,
      documentType: attachResult.variables?.['temp_eaccounting_file_attachment']?.documentType,
      // Combined status
      success: true,
      message: `Successfully uploaded file, created voucher, and attached file. Voucher: ${createdVoucher.voucherNumber}, File: ${uploadedFile.filename}`,
      createdUtc: createdVoucher.createdUtc
    };

    onLog({
      level: 'info',
      message: `eAccounting Upload and Create Voucher completed successfully. Voucher: ${createdVoucher.voucherNumber}, File: ${uploadedFile.filename}`,
      stepId: step.id
    });

    return {
      success: true,
      variables: {
        [variableName]: combinedResult
      }
    };

  } catch (error) {
    onLog({
      level: 'error',
      message: `eAccounting Upload and Create Voucher failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      stepId: step.id
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred during combined operation'
    };
  }
}
