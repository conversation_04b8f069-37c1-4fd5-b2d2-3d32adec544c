import { StepDefinition, StepCategory, STEP_CATEGORIES } from './categories'

export const extractionSteps: StepDefinition[] = [
  {
    type: 'extractText',
    name: 'Extract Text',
    icon: '📝',
    description: 'Extract text from an element'
  },
  {
    type: 'extractAttribute',
    name: 'Extract Attribute',
    icon: '🏷️',
    description: 'Extract attribute value from an element'
  },
  {
    type: 'takeScreenshot',
    name: 'Screenshot',
    icon: '📸',
    description: 'Take a screenshot'
  },
  {
    type: 'downloadFile',
    name: 'Download File',
    icon: '📥',
    description: 'Download a file and optionally convert to base64'
  }
]

export const extractionCategory: StepCategory = {
  name: STEP_CATEGORIES.DATA_EXTRACTION,
  icon: '📊',
  steps: extractionSteps
}
