# Fortnox Filhantering - <PERSON>empel och Best Practices

Detta dokument beskriver hur man använder Fortnox filhanteringsfunktioner i RPA-flöden för att ladda upp filer och koppla dem till verifikationer.

## Översikt

Fortnox filhantering består av fyra huvudsakliga steg:

1. **`fortnoxUploadFile`** - Ladda upp fil till Fortnox arkiv
2. **`fortnoxAttachFileToVoucher`** - Koppla fil till befintlig verifikation
3. **`fortnoxCreateVoucher`** - Skapa verifikation (med optional filkoppling)
4. **`fortnoxUploadAndCreateVoucher`** - Kombinerat steg för fil + verifikation

## Förutsättningar

- Fortnox OAuth2-token konfigurerat för kunden
- Filer måste vara i base64-format (från `downloadFile` eller liknande steg)
- Giltiga Fortnox API-beh<PERSON>righeter för arkiv och verifikationer

## Grundläggande Arbetsflöden

### 1. Enkel <PERSON>

```yaml
# Steg 1: Ladda ner fil från webbsida
- type: downloadFile
  name: "Ladda ner faktura"
  triggerSelector: ".download-invoice"
  variableName: "invoice-file"
  saveToFile: false

# Steg 2: Ladda upp till Fortnox
- type: fortnoxUploadFile
  name: "Ladda upp faktura till Fortnox"
  inputVariable: "invoice-file"
  filename: "faktura-${invoice-number}.pdf"
  description: "Inköpsfaktura från leverantör"
  variableName: "fortnox-file"
```

### 2. Skapa Verifikation med Filkoppling

```yaml
# Steg 1: Ladda upp fil
- type: fortnoxUploadFile
  name: "Ladda upp kvitto"
  inputVariable: "receipt-file"
  variableName: "uploaded-receipt"

# Steg 2: Skapa verifikation med AI
- type: fortnoxCreateVoucher
  name: "Skapa verifikation"
  inputVariable: "receipt-data"
  aiPrompt: "Skapa en utgiftsverifikation för detta kvitto. Använd konto 6250 för kostnader och 2440 för leverantörsskuld."
  fileIds: ["${uploaded-receipt.fileId}"]
  variableName: "voucher-result"
```

### 3. Kombinerad Operation (Rekommenderat)

```yaml
# Ett steg som gör allt
- type: fortnoxUploadAndCreateVoucher
  name: "Ladda upp och skapa verifikation"
  fileInputVariable: "invoice-file"
  filename: "faktura-${invoice-number}.pdf"
  fileDescription: "Inköpsfaktura"
  voucherInputVariable: "invoice-data"
  aiPrompt: "Skapa en inköpsverifikation med moms. Använd konto 4010 för inköp, 2640 för ingående moms och 2440 för leverantörsskuld."
  voucherDescription: "Inköpsfaktura ${invoice-number}"
  voucherSeries: "A"
  variableName: "complete-result"
```

## Variabelhantering

### Input-variabler

Filstegen förväntar sig base64-kodade filer från tidigare steg:

```yaml
# Från downloadFile
variables:
  my-file: "base64-content..."
  my-file_filename: "document.pdf"
  my-file_filepath: "/path/to/file"

# Från fortnoxUploadFile
variables:
  fortnox-file:
    fileId: "abc123"
    filename: "document.pdf"
    size: 12345
    path: "/archive/document.pdf"
```

### Output-variabler

```yaml
# fortnoxUploadFile resultat
{
  "fileId": "abc123",
  "filename": "document.pdf", 
  "size": 12345,
  "path": "/archive/document.pdf",
  "fullResponse": { ... }
}

# fortnoxCreateVoucher resultat (med fil)
{
  "voucherNumber": 123,
  "voucherSeries": "A",
  "totalAmount": 1250.00,
  "attachedFiles": ["abc123"],
  "aiExplanation": "Inköpsverifikation skapad...",
  "fullResponse": { ... }
}

# fortnoxUploadAndCreateVoucher resultat
{
  "fileId": "abc123",
  "filename": "document.pdf",
  "voucherNumber": 123,
  "voucherSeries": "A", 
  "totalAmount": 1250.00,
  "attachedFileId": "abc123",
  "aiExplanation": "Kombinerad operation...",
  "fullVoucherResponse": { ... },
  "fullFileResponse": { ... }
}
```

## Best Practices

### 1. Filnamnshantering

```yaml
# Använd dynamiska filnamn
filename: "faktura-${supplier-name}-${date}.pdf"

# Eller låt systemet använda originalnamnet
# filename: "" # Använder {inputVariable}_filename
```

### 2. Felhantering

```yaml
# Använd beskrivande namn för felsökning
- type: fortnoxUploadFile
  name: "Ladda upp faktura från ${supplier-name}"
  inputVariable: "invoice-file"
  description: "Faktura ${invoice-number} från ${supplier-name}"
```

### 3. AI-prompter för Verifikationer

```yaml
# Specifika prompter ger bättre resultat
aiPrompt: |
  Skapa en inköpsverifikation för denna faktura:
  - Använd konto 4010 för varor/tjänster
  - Använd konto 2640 för ingående moms (25%)
  - Använd konto 2440 för leverantörsskuld
  - Kontrollera att verifikationen är balanserad
  - Inkludera leverantörsnamn i beskrivningen
```

### 4. Verifikationsserier

```yaml
# Använd rätt serie för olika typer
voucherSeries: "A"  # Allmänna verifikationer
voucherSeries: "B"  # Bankverifikationer  
voucherSeries: "C"  # Kassaverifikationer
voucherSeries: "D"  # Diverse verifikationer
```

## Komplett Exempel: Fakturahantering

```yaml
flow:
  name: "Automatisk Fakturahantering"
  steps:
    # 1. Navigera till leverantörsportal
    - type: navigate
      name: "Gå till leverantörsportal"
      url: "https://portal.supplier.com/invoices"

    # 2. Logga in
    - type: fillPassword
      name: "Logga in"
      usernameSelector: "#username"
      passwordSelector: "#password"
      credentialId: "supplier-login"

    # 3. Extrahera fakturadata
    - type: extractText
      name: "Hämta fakturanummer"
      selector: ".invoice-number"
      variableName: "invoice-number"

    - type: extractText
      name: "Hämta leverantörsnamn"
      selector: ".supplier-name"
      variableName: "supplier-name"

    - type: extractText
      name: "Hämta totalbelopp"
      selector: ".total-amount"
      variableName: "total-amount"

    # 4. Ladda ner faktura-PDF
    - type: downloadFile
      name: "Ladda ner faktura"
      triggerSelector: ".download-pdf"
      variableName: "invoice-pdf"
      saveToFile: false

    # 5. Kombinerad uppladdning och verifikationsskapande
    - type: fortnoxUploadAndCreateVoucher
      name: "Skapa verifikation med bifogad faktura"
      fileInputVariable: "invoice-pdf"
      filename: "faktura-${supplier-name}-${invoice-number}.pdf"
      fileDescription: "Faktura ${invoice-number} från ${supplier-name}"
      voucherInputVariable: "total-amount"
      aiPrompt: |
        Skapa en inköpsverifikation för faktura ${invoice-number} från ${supplier-name}:
        - Totalbelopp: ${total-amount} SEK (inklusive 25% moms)
        - Använd konto 4010 för inköp av varor/tjänster
        - Använd konto 2640 för ingående moms
        - Använd konto 2440 för leverantörsskuld
        - Sätt leverantörsnamn i beskrivningen
      voucherDescription: "Inköpsfaktura ${invoice-number} - ${supplier-name}"
      voucherSeries: "A"
      variableName: "voucher-with-file"

    # 6. Logga resultat
    - type: takeScreenshot
      name: "Ta skärmdump av resultat"
      variableName: "completion-screenshot"
```

## Felsökning

### Vanliga Problem

1. **"No file content found in variable"**
   - Kontrollera att `downloadFile` lyckades
   - Verifiera variabelnamnet

2. **"Invalid base64 data"**
   - Filen kanske inte laddades ner korrekt
   - Kontrollera att `saveToFile: false` är satt

3. **"Fortnox API error 400"**
   - Kontrollera OAuth2-token
   - Verifiera filformat och storlek

4. **"Voucher is not balanced"**
   - AI-prompten behöver vara mer specifik
   - Kontrollera input-data kvalitet

### Debug-tips

```yaml
# Lägg till logging för felsökning
- type: takeScreenshot
  name: "Debug: Före filuppladdning"
  variableName: "debug-before"

# Använd beskrivande variabelnamn
variableName: "invoice-file-from-supplier-portal"
```

## API-referens

Se [Fortnox API-dokumentation](https://api.fortnox.se/apidocs) för detaljer om:
- Archive API (`/3/archive`)
- Voucher File Connections API (`/3/voucherfileconnections`)
- Vouchers API (`/3/vouchers`)
