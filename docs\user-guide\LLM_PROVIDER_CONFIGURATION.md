# LLM Provider Konfigurationsguide

## Översikt

RPA-applikationen använder en flexibel LLM provider-arkitektur som gör det enkelt att byta mellan olika AI-providers och modeller utan kodändringar. Denna guide beskriver hur du konfigurerar och använder olika LLM-providers.

## Stödda Providers

### 1. OpenAI Provider
- **Provider ID**: `openai`
- **Stödda modeller**: `gpt-4o-mini`, `o1-mini`
- **Fördelar**: Direkt tillgång till senaste modeller, enkel setup
- **Användning**: Utveckling och produktion

### 2. Azure OpenAI Provider
- **Provider ID**: `azure`
- **Stödda modeller**: Anpassningsbara deployments
- **Fördelar**: Enterprise-säkerhet, compliance, regional hosting
- **Användning**: Enterprise-miljöer, specifika säkerhetskrav

## Grundläggande Konfiguration

### Miljövariabler

Alla LLM-inställningar konfigureras via miljövariabler i `backend/.env`:

```bash
# LLM Provider Configuration
LLM_PROVIDER=openai                   # Aktiv provider: 'openai' | 'azure'
LLM_DEFAULT_MODEL=gpt-4o-mini        # Standardmodell för alla AI-anrop
LLM_FALLBACK_MODEL=gpt-4o-mini       # Fallback om default misslyckas

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_api_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Azure Model Deployments
AZURE_MODEL_GPT4O_MINI=gpt-4o-mini-deployment
AZURE_MODEL_O1_MINI=o1-mini-deployment
```

## OpenAI Provider Setup

### 1. Skaffa API-nyckel
1. Gå till [OpenAI Platform](https://platform.openai.com/)
2. Skapa konto eller logga in
3. Navigera till API Keys
4. Skapa ny API-nyckel
5. Kopiera nyckeln (visas bara en gång)

### 2. Konfigurera miljövariabler
```bash
# Grundläggande OpenAI-setup
LLM_PROVIDER=openai
LLM_DEFAULT_MODEL=gpt-4o-mini
LLM_FALLBACK_MODEL=gpt-4o-mini
OPENAI_API_KEY=sk-proj-your-actual-api-key-here
```

### 3. Verifiera konfiguration
```bash
# Testa health endpoint
curl http://localhost:3002/api/ai-assistant/health

# Förväntat svar
{
  "success": true,
  "data": {
    "aiStatus": "connected",
    "provider": "openai",
    "hasApiKey": true
  }
}
```

## Azure OpenAI Provider Setup

### 1. Skapa Azure OpenAI Resource
1. Logga in på [Azure Portal](https://portal.azure.com/)
2. Skapa ny "Azure OpenAI" resource
3. Välj region och pricing tier
4. Vänta på deployment (kan ta några minuter)

### 2. Konfigurera Model Deployments
1. Gå till din Azure OpenAI resource
2. Navigera till "Model deployments"
3. Skapa deployments för önskade modeller:
   - **gpt-4o-mini**: För standard AI-funktioner
   - **o1-mini**: För avancerad reasoning (om tillgänglig)

### 3. Hämta konfigurationsdata
```bash
# Från Azure Portal -> Keys and Endpoint
AZURE_OPENAI_API_KEY=your-azure-api-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/

# Från Model deployments
AZURE_MODEL_GPT4O_MINI=your-gpt4o-mini-deployment-name
```

### 4. Konfigurera miljövariabler
```bash
# Azure OpenAI-setup
LLM_PROVIDER=azure
LLM_DEFAULT_MODEL=gpt-4o-mini
LLM_FALLBACK_MODEL=gpt-4o-mini

AZURE_OPENAI_API_KEY=your-azure-api-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_MODEL_GPT4O_MINI=your-deployment-name
```

## Modellkonfiguration

### Tillgängliga Modeller

#### gpt-4o-mini
- **Beskrivning**: Snabb och kostnadseffektiv modell
- **Användning**: Standard för alla AI-funktioner
- **Max tokens**: 128,000
- **Kostnad**: Låg
- **Streaming**: Stöds

#### o1-mini
- **Beskrivning**: Avancerad reasoning-modell
- **Användning**: Komplex problemlösning
- **Max tokens**: 65,536
- **Kostnad**: Högre
- **Streaming**: Stöds ej

### Byta Modell

```bash
# Byta till o1-mini för bättre reasoning
LLM_DEFAULT_MODEL=o1-mini

# Behåll gpt-4o-mini som fallback
LLM_FALLBACK_MODEL=gpt-4o-mini
```

## Provider-byte

### Från OpenAI till Azure
```bash
# 1. Ändra provider
LLM_PROVIDER=azure

# 2. Säkerställ Azure-konfiguration
AZURE_OPENAI_API_KEY=your-azure-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_MODEL_GPT4O_MINI=your-deployment

# 3. Starta om servern
npm run dev
```

### Från Azure till OpenAI
```bash
# 1. Ändra provider
LLM_PROVIDER=openai

# 2. Säkerställ OpenAI-konfiguration
OPENAI_API_KEY=your-openai-key

# 3. Starta om servern
npm run dev
```

## Felsökning

### Vanliga Problem

#### "LLM provider not configured"
```bash
# Kontrollera att rätt miljövariabler är satta
echo $LLM_PROVIDER
echo $OPENAI_API_KEY  # för OpenAI
echo $AZURE_OPENAI_API_KEY  # för Azure
```

#### "Model not supported"
```bash
# Kontrollera att modellen finns i ModelRegistry
# Tillgängliga modeller för OpenAI: gpt-4o-mini, o1-mini
# Tillgängliga modeller för Azure: beror på dina deployments
```

#### "API key invalid"
```bash
# Testa API-nyckel direkt
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
```

### Health Check Debugging

```bash
# Detaljerad health check
curl -v http://localhost:3002/api/ai-assistant/health

# Möjliga svar:
# aiStatus: "connected" - Allt fungerar
# aiStatus: "error" - Problem med provider/API-nyckel
# provider: "openai" | "azure" - Aktiv provider
# hasApiKey: true | false - API-nyckel status
```

## Best Practices

### Säkerhet
- Använd aldrig API-nycklar i kod
- Lagra nycklar i miljövariabler eller secrets management
- Rotera API-nycklar regelbundet
- Använd olika nycklar för dev/staging/prod
- **VIKTIGT**: Systemet byter ALDRIG provider automatiskt - om en provider misslyckas kastas ett fel istället för att falla tillbaka till en annan provider

### Prestanda
- Använd `gpt-4o-mini` för standard-funktioner (snabbare, billigare)
- Reservera `o1-mini` för komplex reasoning
- Konfigurera lämpliga fallback-modeller
- Övervaka API-användning och kostnader

### Tillförlitlighet
- Konfigurera alltid fallback-modell (inom samma provider)
- Testa provider-byte i staging innan produktion
- Övervaka health endpoints
- Säkerställ att vald provider är korrekt konfigurerad - systemet byter INTE provider automatiskt

## Produktionsdeploy

### Miljövariabler för Produktion
```bash
# Produktionskonfiguration
LLM_PROVIDER=azure  # Rekommenderat för enterprise
LLM_DEFAULT_MODEL=gpt-4o-mini
LLM_FALLBACK_MODEL=gpt-4o-mini

# Azure (säkrare för produktion)
AZURE_OPENAI_API_KEY=${AZURE_API_KEY_SECRET}
AZURE_OPENAI_ENDPOINT=https://prod-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_MODEL_GPT4O_MINI=prod-gpt4o-mini
```

### Monitoring
- Övervaka health endpoint: `/api/ai-assistant/health`
- Logga provider-byten och fel
- Sätt upp alerts för API-fel
- Övervaka token-användning och kostnader

## Framtida Utbyggnad

Arkitekturen är designad för enkel utbyggnad:

### Nya Providers
- Lägg till ny provider-klass i `backend/src/services/llm/providers/`
- Registrera i `LLMProviderFactory`
- Uppdatera `ModelRegistry` med nya modeller

### Nya Modeller
- Lägg till modellkonfiguration i `ModelRegistry`
- Uppdatera provider-klasser vid behov
- Testa kompatibilitet

Denna flexibla arkitektur säkerställer att applikationen kan anpassas till nya AI-providers och modeller utan större kodändringar.
