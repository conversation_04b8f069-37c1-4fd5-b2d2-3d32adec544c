import { StepDefinition, StepCategory, STEP_CATEGORIES } from './categories'

export const waitingSteps: StepDefinition[] = [
  {
    type: 'waitForSelector',
    name: 'Wait for Element',
    icon: '👁️',
    description: 'Wait for an element to appear'
  },
  {
    type: 'waitForTimeout',
    name: 'Wait for Time',
    icon: '⏰',
    description: 'Wait for a specific duration'
  },
  {
    type: 'waitForUrl',
    name: 'Wait for URL',
    icon: '🌐',
    description: 'Wait for URL to match pattern'
  }
]

export const waitingCategory: StepCategory = {
  name: STEP_CATEGORIES.WAITING,
  icon: '⏳',
  steps: waitingSteps
}
