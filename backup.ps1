# RPA Production Backup Script
# Kör dagligen via Task Scheduler

$BackupDir = "C:\RPA_Backups"
$Date = Get-Date -Format "yyyy-MM-dd_HH-mm"
$BackupPath = "$BackupDir\rpa_backup_$Date"

# Skapa backup-mapp
New-Item -ItemType Directory -Force -Path $BackupPath

# Backup Redis data
Write-Host "Backing up Redis data..."
docker run --rm -v automation_app_redis_data_prod:/data -v ${BackupPath}:/backup alpine tar czf /backup/redis_data.tar.gz -C /data .

# Backup application data
Write-Host "Backing up application data..."
Copy-Item -Recurse -Force ".\backend\data" "$BackupPath\app_data"
Copy-Item -Recurse -Force ".\screenshots" "$BackupPath\screenshots"
Copy-Item -Recurse -Force ".\downloads" "$BackupPath\downloads"

# Backup configuration
Write-Host "Backing up configuration..."
Copy-Item ".env.docker" "$BackupPath\.env.docker"
Copy-Item "docker-compose.prod.yml" "$BackupPath\docker-compose.prod.yml"

# Rensa gamla backups (behåll 7 dagar)
Get-ChildItem $BackupDir -Directory | Where-Object {$_.CreationTime -lt (Get-Date).AddDays(-7)} | Remove-Item -Recurse -Force

Write-Host "Backup completed: $BackupPath"
