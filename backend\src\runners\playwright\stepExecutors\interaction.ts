import { Page } from 'playwright';
import { RpaStep, ExecutionLog } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { credentialService } from '../../../services/credentialService';

/**
 * Interaction step executors for PlaywrightRunner
 */

export interface InteractionExecutorContext {
  page: Page;
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
}

/**
 * Execute click step
 */
export async function executeClick(
  step: RpaStep & { selector: string; button?: 'left' | 'right' | 'middle'; clickCount?: number; force?: boolean },
  context: InteractionExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedClickSelector = interpolateVariables(step.selector, variables);
  await page.click(interpolatedClickSelector, {
    button: step.button || 'left',
    clickCount: step.clickCount || 1,
    force: step.force,
    timeout
  });

  onLog({
    level: 'info',
    message: `Clicked element: ${interpolatedClickSelector}`,
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute fill step
 */
export async function executeFill(
  step: RpaStep & { selector: string; value: string; force?: boolean },
  context: InteractionExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedFillValue = interpolateVariables(step.value, variables);
  const interpolatedFillSelector = interpolateVariables(step.selector, variables);
  await page.fill(interpolatedFillSelector, interpolatedFillValue, {
    force: step.force,
    timeout
  });

  onLog({
    level: 'info',
    message: `Filled element ${interpolatedFillSelector} with value: ${interpolatedFillValue}`,
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute type step
 */
export async function executeType(
  step: RpaStep & { selector: string; text: string },
  context: InteractionExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;

  const interpolatedTypeText = interpolateVariables(step.text, variables);
  const interpolatedTypeSelector = interpolateVariables(step.selector, variables);
  await page.locator(interpolatedTypeSelector).fill(interpolatedTypeText);

  onLog({
    level: 'info',
    message: `Typed text in element ${interpolatedTypeSelector}: ${interpolatedTypeText}`,
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute selectOption step
 */
export async function executeSelectOption(
  step: RpaStep & { selector: string; value?: string; label?: string; index?: number },
  context: InteractionExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedSelectSelector = interpolateVariables(step.selector, variables);
  const selectOptions: any = {};
  if (step.value) selectOptions.value = interpolateVariables(step.value, variables);
  if (step.label) selectOptions.label = interpolateVariables(step.label, variables);
  if (step.index !== undefined) selectOptions.index = step.index;

  await page.selectOption(interpolatedSelectSelector, selectOptions, { timeout });

  onLog({
    level: 'info',
    message: `Selected option in ${interpolatedSelectSelector}`,
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute check step
 */
export async function executeCheck(
  step: RpaStep & { selector: string; force?: boolean },
  context: InteractionExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedCheckSelector = interpolateVariables(step.selector, variables);
  await page.check(interpolatedCheckSelector, {
    force: step.force,
    timeout
  });

  onLog({
    level: 'info',
    message: `Checked element: ${interpolatedCheckSelector}`,
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute uncheck step
 */
export async function executeUncheck(
  step: RpaStep & { selector: string; force?: boolean },
  context: InteractionExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedUncheckSelector = interpolateVariables(step.selector, variables);
  await page.uncheck(interpolatedUncheckSelector, {
    force: step.force,
    timeout
  });

  onLog({
    level: 'info',
    message: `Unchecked element: ${interpolatedUncheckSelector}`,
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute fillPassword step
 */
export async function executeFillPassword(
  step: RpaStep & { selector: string; credentialId: string },
  context: InteractionExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolatedPasswordSelector = interpolateVariables(step.selector, variables);
  const passwordData = await credentialService.getDecryptedPassword(step.credentialId);
  if (!passwordData) {
    throw new Error(`Password credential with id ${step.credentialId} not found`);
  }

  await page.fill(interpolatedPasswordSelector, passwordData.password, {
    timeout
  });

  onLog({
    level: 'info',
    message: `Filled password in element: ${interpolatedPasswordSelector}`,
    stepId: step.id
  });

  return { success: true };
}

/**
 * Execute fill2FA step
 */
export async function executeFill2FA(
  step: RpaStep & { selector: string; credentialId: string },
  context: InteractionExecutorContext
): Promise<StepExecutionResult> {
  const { page, variables, onLog, interpolateVariables } = context;
  const timeout = step.timeout || 30000;

  const interpolated2FASelector = interpolateVariables(step.selector, variables);
  const totpCode = await credentialService.generateTOTPCode(step.credentialId);
  if (!totpCode) {
    throw new Error(`2FA credential with id ${step.credentialId} not found`);
  }

  await page.fill(interpolated2FASelector, totpCode, {
    timeout
  });

  onLog({
    level: 'info',
    message: `Filled 2FA code in element: ${interpolated2FASelector}`,
    stepId: step.id
  });

  return { success: true };
}
