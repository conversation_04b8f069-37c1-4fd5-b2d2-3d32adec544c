// Template för step-editor komponent
// Skapa som frontend/src/components/flow-editor/step-editors/{{STEP_CATEGORY}}/{{PASCAL_CASE}}StepEditor.tsx

import React from 'react';
import { {{PASCAL_CASE}}Step } from '../../../../../shared/src/types/steps/{{STEP_CATEGORY}}';
import { BaseStepEditorProps } from '../base/BaseStepEditor';

interface {{PASCAL_CASE}}StepEditorProps extends BaseStepEditorProps {
  step: {{PASCAL_CASE}}Step;
}

export const {{PASCAL_CASE}}StepEditor: React.FC<{{PASCAL_CASE}}StepEditorProps> = ({
  step,
  onStepChange,
  variables = []
}) => {
  const handleChange = (field: keyof {{PASCAL_CASE}}Step, value: any) => {
    onStepChange({
      ...step,
      [field]: value
    });
  };

  return (
    <div className="step-editor">
      {/* Grundläggande fält */}
      <div className="form-row">
        <div className="form-group">
          <label>Namn</label>
          <input
            type="text"
            value={step.name || ''}
            onChange={(e) => handleChange('name', e.target.value)}
            placeholder="Namn på steget"
            className="form-input"
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>Beskrivning</label>
          <textarea
            value={step.description || ''}
            onChange={(e) => handleChange('description', e.target.value)}
            placeholder="Beskrivning av vad steget gör"
            className="form-textarea"
            rows={2}
          />
        </div>
      </div>

      {/* Step-specifika fält - anpassa efter behov */}
      
      {/* För selector-baserade steg: */}
      {/* <div className="form-row">
        <div className="form-group">
          <label>CSS Selector</label>
          <input
            type="text"
            value={step.selector || ''}
            onChange={(e) => handleChange('selector', e.target.value)}
            placeholder="CSS selector för element"
            className="form-input"
          />
        </div>
      </div> */}

      {/* För value-baserade steg: */}
      {/* <div className="form-row">
        <div className="form-group">
          <label>Värde</label>
          <input
            type="text"
            value={step.value || ''}
            onChange={(e) => handleChange('value', e.target.value)}
            placeholder="Värde att använda"
            className="form-input"
          />
        </div>
      </div> */}

      {/* För timeout-inställningar: */}
      {/* <div className="form-row">
        <div className="form-group">
          <label>Timeout (ms)</label>
          <input
            type="number"
            value={step.timeout || 5000}
            onChange={(e) => handleChange('timeout', parseInt(e.target.value))}
            min="0"
            max="60000"
            className="form-input"
          />
        </div>
        <div className="form-group">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={step.waitForSelector || false}
              onChange={(e) => handleChange('waitForSelector', e.target.checked)}
            />
            Vänta på element
          </label>
        </div>
      </div> */}

      {/* För AI-steg: */}
      {/* <div className="form-row">
        <div className="form-group">
          <label>AI Prompt</label>
          <textarea
            value={step.prompt || ''}
            onChange={(e) => handleChange('prompt', e.target.value)}
            placeholder="Prompt för AI-modellen"
            className="form-textarea"
            rows={4}
          />
        </div>
      </div>

      <div className="form-row">
        <div className="form-group">
          <label>AI Modell</label>
          <select
            value={step.model || 'gpt-4o-mini'}
            onChange={(e) => handleChange('model', e.target.value)}
            className="form-select"
          >
            <option value="gpt-4o-mini">GPT-4o Mini</option>
            <option value="gpt-4o">GPT-4o</option>
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
          </select>
        </div>
      </div> */}

      {/* För API-steg: */}
      {/* <div className="form-row">
        <div className="form-group">
          <label>URL</label>
          <input
            type="url"
            value={step.url || ''}
            onChange={(e) => handleChange('url', e.target.value)}
            placeholder="https://api.example.com/endpoint"
            className="form-input"
          />
        </div>
        <div className="form-group">
          <label>HTTP Metod</label>
          <select
            value={step.method || 'GET'}
            onChange={(e) => handleChange('method', e.target.value)}
            className="form-select"
          >
            <option value="GET">GET</option>
            <option value="POST">POST</option>
            <option value="PUT">PUT</option>
            <option value="DELETE">DELETE</option>
          </select>
        </div>
      </div> */}
    </div>
  );
};

// Lägg till i StepEditorRegistry.tsx:
// case '{{STEP_TYPE}}':
//   return <{{PASCAL_CASE}}StepEditor {...props} step={step as {{PASCAL_CASE}}Step} />;
