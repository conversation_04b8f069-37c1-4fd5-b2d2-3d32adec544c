// Runner interfaces and types

export type RunnerType = 'playwright' | 'ai' | 'api';

export interface RunnerConfig {
  type: RunnerType;
  settings?: Record<string, any>;
}

export interface RunnerResult {
  success: boolean;
  data?: any;
  error?: string;
  variables?: Record<string, any>;
}

export interface IRunner {
  type: RunnerType;
  canExecuteStep(stepType: string): boolean;
  executeStep(step: any, context: any): Promise<RunnerResult>;
  initialize?(config?: any): Promise<void>;
  cleanup?(): Promise<void>;
}
