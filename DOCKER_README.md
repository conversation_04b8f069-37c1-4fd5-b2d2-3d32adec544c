# 🐳 Docker Quick Start Guide

Snabbguide för att köra RPA-projektet med Docker.

## ⚡ Snabbstart (5 minuter)

```bash
# 1. <PERSON><PERSON><PERSON> milj<PERSON>variabler
cp .env.docker.example .env.docker

# 2. Redigera .env.docker - lägg till din OpenAI API-nyckel
nano .env.docker  # eller din favorit-editor

# 3. Bygg och starta
npm run docker:build
npm run docker:up

# 4. Öppna i webbläsare
# Frontend: http://localhost:3000
# Backend API: http://localhost:3002/health
```

## 📋 Krav

- Docker Desktop eller Docker Engine
- Docker Compose v2+
- Minst 4GB RAM tillgängligt för Docker

## 🔧 Grundläggande kommandon

```bash
# Bygg alla containers
npm run docker:build

# Starta alla tjänster i bakgrunden
npm run docker:up

# Visa loggar (live)
npm run docker:logs

# Stoppa alla tjänster
npm run docker:down

# Starta om en specifik tjänst
docker-compose restart backend
```

## 🌐 Åtkomstpunkter

| Tjänst | URL | Beskrivning |
|--------|-----|-------------|
| Frontend | http://localhost:3000 | React-applikation |
| Backend API | http://localhost:3002 | REST API |
| Health Check | http://localhost:3002/health | API-status |
| Redis | localhost:6379 | Cache/Queue (intern) |

## ⚙️ Miljövariabler

Redigera `.env.docker` för att konfigurera:

### Obligatoriskt - LLM Provider
```bash
# OpenAI (rekommenderat för start)
LLM_PROVIDER=openai
OPENAI_API_KEY=sk-your-openai-api-key-here

# ELLER Azure OpenAI
LLM_PROVIDER=azure
AZURE_OPENAI_API_KEY=your-azure-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
```

### Valfritt - Externa integrationer
```bash
# eEkonomi (Visma)
EEKONOMI_CLIENT_ID=your_client_id
EEKONOMI_CLIENT_SECRET=your_client_secret

# Fortnox
FORTNOX_CLIENT_ID=your_client_id
FORTNOX_CLIENT_SECRET=your_client_secret
```

## 🔍 Felsökning

### Problem: Port redan används
```bash
Error: bind: address already in use
```
**Lösning**: Ändra portar i `docker-compose.yml` eller stoppa tjänster som använder portarna.

### Problem: Container startar inte
```bash
# Kontrollera loggar
docker-compose logs backend

# Kontrollera status
docker-compose ps
```

### Problem: API-anrop misslyckas
1. Kontrollera backend health: `curl http://localhost:3002/health`
2. Verifiera miljövariabler i `.env.docker`
3. Kontrollera loggar: `docker-compose logs backend`

## 🚀 Utveckling

### Endast Redis i Docker (rekommenderat för utveckling)
```bash
# Starta endast Redis
docker-compose up redis -d

# Kör frontend/backend lokalt med hot-reload
npm run dev
```

### Full Docker-utveckling
```bash
# Bygg om efter kodändringar
docker-compose build backend
docker-compose up -d

# Eller bygg om allt
docker-compose build --no-cache
```

## 📊 Monitoring

```bash
# Visa resursutnyttjande
docker stats

# Kontrollera hälsostatus
docker-compose ps

# Följ loggar för specifik tjänst
docker-compose logs -f backend
```

## 🧹 Underhåll

```bash
# Rensa oanvända containers och images
docker system prune

# Rensa allt (VARNING: tar bort alla Docker-data)
docker system prune -a --volumes

# Backup av data
docker run --rm -v rpa-project_backend_data:/data -v $(pwd):/backup alpine tar czf /backup/backup.tar.gz -C /data .
```

## 📚 Mer information

- **Fullständig guide**: [docs/development/docker-deployment.md](docs/development/docker-deployment.md)
- **Arkitektur**: [docs/development/architecture.md](docs/development/architecture.md)
- **Felsökning**: [docs/development/troubleshooting.md](docs/development/troubleshooting.md)

## 🆘 Support

Om du stöter på problem:

1. Kontrollera [felsökningsguiden](docs/development/troubleshooting.md)
2. Kolla loggar: `npm run docker:logs`
3. Verifiera miljövariabler i `.env.docker`
4. Kontakta utvecklingsteamet

---

**Tips**: För första gången, börja med OpenAI API-nyckel för enklaste setup!
