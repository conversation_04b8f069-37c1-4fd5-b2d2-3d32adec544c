import { RpaFlow } from '../flows';
import { ValidationResult, ValidationError } from '../utils';
import { validateStep } from './steps';

export function validateFlow(flow: RpaFlow): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate flow properties
  if (!flow.id || flow.id.trim() === '') {
    errors.push({
      field: 'id',
      message: 'Flow ID is required'
    });
  }

  if (!flow.name || flow.name.trim() === '') {
    errors.push({
      field: 'name',
      message: 'Flow name is required'
    });
  }

  if (!flow.customerId || flow.customerId.trim() === '') {
    errors.push({
      field: 'customerId',
      message: 'Customer ID is required'
    });
  }

  // Note: We allow flows without steps to be saved (empty flows)
  // Steps validation is only done when the flow actually has steps

  // Validate each step
  if (flow.steps) {
    flow.steps.forEach((step, index) => {
      const stepValidation = validateStep(step);
      if (!stepValidation.isValid) {
        stepValidation.errors.forEach((error: ValidationError) => {
          errors.push({
            field: `steps[${index}].${error.field}`,
            message: `Step ${index + 1}: ${error.message}`
          });
        });
      }
    });
  }

  // Validate settings
  if (flow.settings) {
    if (flow.settings.viewport) {
      if (flow.settings.viewport.width <= 0 || flow.settings.viewport.height <= 0) {
        errors.push({
          field: 'settings.viewport',
          message: 'Viewport dimensions must be positive'
        });
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
