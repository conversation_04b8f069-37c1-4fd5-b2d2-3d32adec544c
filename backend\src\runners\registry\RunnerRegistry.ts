import { IRun<PERSON> } from '../base/IRunner';
import { RunnerType, getRunnerForStep, isRunnerImplemented } from './stepTypes';

/**
 * Constructor type for runner classes
 */
export type RunnerConstructor = new () => IRunner;

/**
 * Registry for managing runner types and their implementations
 * Enhanced with auto-discovery capabilities
 */
export class RunnerRegistry {
  private static instance: RunnerRegistry;
  private runners: Map<RunnerType, RunnerConstructor> = new Map();
  private runnerInstances: Map<string, IRunner> = new Map();

  private constructor() {}

  /**
   * Get singleton instance of the registry
   */
  public static getInstance(): RunnerRegistry {
    if (!RunnerRegistry.instance) {
      RunnerRegistry.instance = new RunnerRegistry();
    }
    return RunnerRegistry.instance;
  }

  /**
   * Register a runner implementation for a specific runner type
   */
  public registerRunner(runnerType: RunnerType, runnerClass: RunnerConstructor): void {
    this.runners.set(runnerType, runnerClass);
    console.log(`📋 Registered runner: ${runnerType} -> ${runnerClass.name}`);
  }

  /**
   * Auto-discover and register runners from a directory
   * This enables automatic registration of new runners
   */
  public async autoDiscoverRunners(runnersPath: string): Promise<void> {
    // This would scan the runners directory and automatically register
    // any runners that follow the naming convention
    // Implementation would be added in future iterations
    console.log(`🔍 Auto-discovery from ${runnersPath} - Feature coming soon`);
  }

  /**
   * Get a runner constructor for a specific runner type
   */
  public getRunnerConstructor(runnerType: RunnerType): RunnerConstructor {
    const runnerConstructor = this.runners.get(runnerType);
    if (!runnerConstructor) {
      throw new Error(`No runner registered for type: ${runnerType}`);
    }
    return runnerConstructor;
  }

  /**
   * Create a new runner instance for a specific runner type
   */
  public createRunner(runnerType: RunnerType): IRunner {
    const RunnerClass = this.getRunnerConstructor(runnerType);
    return new RunnerClass();
  }

  /**
   * Get or create a cached runner instance for a specific execution
   * This allows reusing runners within the same execution context
   */
  public getOrCreateRunner(executionId: string, runnerType: RunnerType): IRunner {
    const cacheKey = `${executionId}-${runnerType}`;
    
    let runner = this.runnerInstances.get(cacheKey);
    if (!runner) {
      runner = this.createRunner(runnerType);
      this.runnerInstances.set(cacheKey, runner);
    }
    
    return runner;
  }

  /**
   * Get runner type for a specific step type
   */
  public getRunnerTypeForStep(stepType: string): RunnerType {
    return getRunnerForStep(stepType);
  }

  /**
   * Get runner instance for a specific step type within an execution context
   */
  public getRunnerForStep(executionId: string, stepType: string): IRunner {
    const runnerType = this.getRunnerTypeForStep(stepType);
    return this.getOrCreateRunner(executionId, runnerType);
  }

  /**
   * Check if a runner is registered for a specific type
   */
  public isRunnerRegistered(runnerType: RunnerType): boolean {
    return this.runners.has(runnerType);
  }

  /**
   * Check if a step type is supported (has a registered runner)
   */
  public isStepTypeSupported(stepType: string): boolean {
    try {
      const runnerType = getRunnerForStep(stepType);
      return this.isRunnerRegistered(runnerType) && isRunnerImplemented(runnerType);
    } catch {
      return false;
    }
  }

  /**
   * Get all registered runner types
   */
  public getRegisteredRunnerTypes(): RunnerType[] {
    return Array.from(this.runners.keys());
  }

  /**
   * Clean up runner instances for a specific execution
   */
  public async cleanupExecution(executionId: string): Promise<void> {
    const keysToRemove: string[] = [];
    
    for (const [key, runner] of this.runnerInstances.entries()) {
      if (key.startsWith(`${executionId}-`)) {
        try {
          await runner.cleanup();
        } catch (error) {
          console.error(`Error cleaning up runner ${key}:`, error);
        }
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => this.runnerInstances.delete(key));
    console.log(`🧹 Cleaned up ${keysToRemove.length} runner instances for execution: ${executionId}`);
  }

  /**
   * Clean up all runner instances
   */
  public async cleanupAll(): Promise<void> {
    const cleanupPromises = Array.from(this.runnerInstances.values()).map(runner => 
      runner.cleanup().catch(error => 
        console.error('Error cleaning up runner:', error)
      )
    );
    
    await Promise.all(cleanupPromises);
    this.runnerInstances.clear();
    console.log('🧹 Cleaned up all runner instances');
  }

  /**
   * Get statistics about the registry
   */
  public getStats(): {
    registeredRunners: number;
    activeInstances: number;
    runnerTypes: RunnerType[];
  } {
    return {
      registeredRunners: this.runners.size,
      activeInstances: this.runnerInstances.size,
      runnerTypes: this.getRegisteredRunnerTypes()
    };
  }

  /**
   * Reset the registry (mainly for testing)
   */
  public reset(): void {
    this.runners.clear();
    this.runnerInstances.clear();
  }
}

/**
 * Get the global runner registry instance
 */
export function getRunnerRegistry(): RunnerRegistry {
  return RunnerRegistry.getInstance();
}
