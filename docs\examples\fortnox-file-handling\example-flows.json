{"flows": [{"name": "Automatisk Fakturahantering - Komplett", "description": "Laddar ner faktura från leverantörsportal, laddar upp till Fortnox och skapar verifikation automatiskt", "steps": [{"id": "step_1", "type": "navigate", "url": "https://portal.supplier.com/invoices", "timeout": 30000, "description": "Navigera till leverantörsportal"}, {"id": "step_2", "type": "fillPassword", "usernameSelector": "#username", "passwordSelector": "#password", "credentialId": "supplier-login", "timeout": 30000, "description": "<PERSON>gga in på leverantörsportalen"}, {"id": "step_3", "type": "extractText", "selector": ".invoice-number", "variableName": "var-invoice-number", "timeout": 30000, "description": "Extrahera fakturanummer"}, {"id": "step_4", "type": "extractText", "selector": ".supplier-name", "variableName": "var-supplier-name", "timeout": 30000, "description": "<PERSON><PERSON>a <PERSON>"}, {"id": "step_5", "type": "extractText", "selector": ".total-amount", "variableName": "var-total-amount", "timeout": 30000, "description": "Extrahera totalbelopp"}, {"id": "step_6", "type": "extractText", "selector": ".due-date", "variableName": "var-due-date", "timeout": 30000, "description": "Extrahera förfallodatum"}, {"id": "step_7", "type": "downloadFile", "triggerSelector": ".download-pdf", "variableName": "var-invoice-pdf", "saveToFile": false, "timeout": 60000, "description": "Ladda ner faktura-PDF"}, {"id": "step_8", "type": "fortnoxUploadAndCreateVoucher", "fileInputVariable": "var-invoice-pdf", "filename": "faktura-${var-supplier-name}-${var-invoice-number}.pdf", "fileDescription": "Inköpsfaktura ${var-invoice-number} från ${var-supplier-name}", "voucherInputVariable": "var-total-amount", "aiPrompt": "Skapa en inköpsverifikation för faktura ${var-invoice-number} från ${var-supplier-name}. Totalbelopp: ${var-total-amount} SEK (inklusive 25% moms). Använd konto 4010 för inköp av varor/tj<PERSON><PERSON><PERSON>, konto 2640 för ingående moms (25%) och konto 2440 för leverantörsskuld. Sätt leverantörsnamn i beskrivningen.", "voucherDescription": "Inköpsfaktura ${var-invoice-number} - ${var-supplier-name}", "voucherSeries": "A", "transactionDate": "${var-due-date}", "variableName": "var-fortnox-result", "timeout": 120000, "description": "Ladda upp faktura och skapa verifikation i Fortnox"}, {"id": "step_9", "type": "takeScreenshot", "variableName": "var-completion-screenshot", "timeout": 30000, "description": "Ta skärmdump av slutfört resultat"}]}, {"name": "Kvittohantering - Separata Steg", "description": "Laddar upp kvitto och skapar verifikation i separata steg för mer kontroll", "steps": [{"id": "step_1", "type": "navigate", "url": "https://expenses.company.com", "timeout": 30000, "description": "Navigera till utgiftssystem"}, {"id": "step_2", "type": "click", "selector": ".receipt-upload", "timeout": 30000, "description": "<PERSON><PERSON><PERSON> på kvitto-uppladdning"}, {"id": "step_3", "type": "downloadFile", "triggerSelector": ".download-receipt", "variableName": "var-receipt-file", "saveToFile": false, "timeout": 60000, "description": "<PERSON>dda ner k<PERSON>tto"}, {"id": "step_4", "type": "extractText", "selector": ".expense-amount", "variableName": "var-expense-amount", "timeout": 30000, "description": "Extrahera utgi<PERSON>p"}, {"id": "step_5", "type": "extractText", "selector": ".expense-category", "variableName": "var-expense-category", "timeout": 30000, "description": "<PERSON><PERSON><PERSON> u<PERSON>giftskategori"}, {"id": "step_6", "type": "fortnoxUploadFile", "inputVariable": "var-receipt-file", "filename": "kvitto-${var-expense-category}-${var-expense-amount}.pdf", "description": "Ladda upp kvitto till Fortnox arkiv", "variableName": "var-uploaded-receipt", "timeout": 60000}, {"id": "step_7", "type": "fortnoxCreateVoucher", "inputVariable": "var-expense-amount", "aiPrompt": "Skapa en utgiftsverifikation för ${var-expense-category}. Belopp: ${var-expense-amount} SEK (inklusive moms). Använd konto 6250 för allmänna kostnader, konto 2640 för ingående moms och konto 1930 för kassa/bank.", "description": "Skapa utgiftsverifikation med bifogat kvitto", "fileIds": ["${var-uploaded-receipt.fileId}"], "voucherSeries": "C", "variableName": "var-expense-voucher", "timeout": 60000}]}, {"name": "Bankutdrag med <PERSON>lk<PERSON>", "description": "Laddar ner bankutdrag och kopplar till befintlig verifikation", "steps": [{"id": "step_1", "type": "navigate", "url": "https://netbank.bank.se", "timeout": 30000, "description": "Navigera till nätbank"}, {"id": "step_2", "type": "fillPassword", "usernameSelector": "#personnummer", "passwordSelector": "#password", "credentialId": "bank-login", "timeout": 30000, "description": "Logga in på nätbanken"}, {"id": "step_3", "type": "click", "selector": "text=<PERSON><PERSON>utdrag", "timeout": 30000, "description": "Gå till kontoutdrag"}, {"id": "step_4", "type": "downloadFile", "triggerSelector": ".download-statement", "variableName": "var-bank-statement", "saveToFile": false, "timeout": 60000, "description": "<PERSON>dda ner <PERSON><PERSON><PERSON><PERSON>"}, {"id": "step_5", "type": "fortnoxUploadFile", "inputVariable": "var-bank-statement", "filename": "bankutdrag-${var-current-month}.pdf", "description": "Ladda upp bankutdrag till Fortnox", "variableName": "var-uploaded-statement", "timeout": 60000}, {"id": "step_6", "type": "fortnoxAttachFileToVoucher", "fileIdVariable": "var-uploaded-statement.fileId", "voucherNumberVariable": "var-existing-voucher-number", "voucherSeriesVariable": "var-existing-voucher-series", "variableName": "var-attachment-result", "timeout": 30000, "description": "Koppla bankutdrag till befintlig verifikation"}]}, {"name": "PDF-extraktion med Verifikation", "description": "Extraherar data från PDF och skapar verifikation med bifogad fil", "steps": [{"id": "step_1", "type": "downloadFile", "triggerSelector": ".download-invoice", "variableName": "var-invoice-pdf", "saveToFile": false, "timeout": 60000, "description": "Ladda ner faktura-PDF"}, {"id": "step_2", "type": "extractPdfValues", "base64Input": "${var-invoice-pdf}", "prompt": "Extrahera följande värden från fakturan: fakturanummer, leverantörsnamn, totalbelopp, momssats, förfallodatum, beskrivning av varor/tjänster", "variableName": "var-invoice-data", "timeout": 60000, "description": "Extrahera data från faktura-PDF"}, {"id": "step_3", "type": "fortnoxUploadAndCreateVoucher", "fileInputVariable": "var-invoice-pdf", "filename": "faktura-${var-invoice-data.fakturanummer}.pdf", "fileDescription": "Faktura ${var-invoice-data.fakturanummer} från ${var-invoice-data.leverantörsnamn}", "voucherInputVariable": "var-invoice-data", "aiPrompt": "Skapa en inköpsverifikation baserat på extraherad fakturadata. Använd följande kontomappning: Inköp av varor/tjänster -> konto 4010, <PERSON>g<PERSON><PERSON><PERSON> moms -> konto 2640, Leverantörsskuld -> konto 2440. Kontrollera att momssatsen stämmer och att verifikationen är balanserad.", "voucherDescription": "Inköpsfaktura ${var-invoice-data.fakturanummer} - ${var-invoice-data.leverantörsnamn}", "voucherSeries": "A", "transactionDate": "${var-invoice-data.förfallodatum}", "variableName": "var-complete-result", "timeout": 120000, "description": "Skapa komplett verifikation med extraherad data och bifogad fil"}]}], "templates": {"common_ai_prompts": {"inköpsfaktura_med_moms": "Skapa en inköpsverifikation med 25% moms. Använd konto 4010 för inköp av varor/tjä<PERSON><PERSON>, konto 2640 för ingående moms och konto 2440 för leverantörsskuld. Kontrollera att verifikationen är balanserad.", "utgiftskvitto": "Skapa en utgiftsverifikation. Använd konto 6250 för allmänna kostnader, konto 2640 för ingående moms (om tillämpligt) och konto 1930 för kassa/bank.", "bankverifikation": "Skapa en bankverifikation. Använd konto 1930 för bankkonto och lämpliga motkonton baserat på transaktionstyp.", "försäljningsfaktura": "Skapa en försäljningsverifikation med 25% moms. Använd konto 3010 för fö<PERSON><PERSON><PERSON><PERSON><PERSON>, konto 2610 för utgående moms och konto 1510 för kundfordringar."}, "common_account_mappings": {"inköp": "4010", "försäljning": "3010", "ingående_moms": "2640", "utgående_moms": "2610", "leverantörsskuld": "2440", "kundfordringar": "1510", "bank": "1930", "kassa": "1910", "allmänna_kostnader": "6250", "kontorsmaterial": "6110", "resor": "6420", "representation": "6330"}}}