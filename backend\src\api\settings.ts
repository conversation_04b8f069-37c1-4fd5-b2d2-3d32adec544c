import { Router, Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import { ApiResponse } from '@rpa-project/shared';
import { setMasterKey, loadMasterKey } from '../utils/encryption';
import { LLMProviderFactory, ProviderType } from '../services/llm/LLMProviderFactory';

const router = Router();

// Validation schema for master key
const masterKeySchema = Joi.object({
  masterKey: Joi.string().required().length(64).pattern(/^[0-9a-fA-F]+$/)
});

// Validation schema for LLM provider settings
const llmProviderSchema = Joi.object({
  provider: Joi.string().valid('openai', 'azure').required(),
  defaultModel: Joi.string().optional()
});

// GET /api/settings/master-key/status - Check if master key exists
router.get('/master-key/status', async (req: Request, res: Response) => {
  try {
    const masterKey = loadMasterKey();
    const exists = masterKey !== null;

    const response: ApiResponse = {
      success: true,
      data: {
        exists,
        message: exists ? 'Master key is configured' : 'Master key not configured'
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Error checking master key status:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// POST /api/settings/master-key - Update master key
router.post('/master-key', async (req: Request, res: Response) => {
  try {
    const { error, value } = masterKeySchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Master key must be exactly 64 hexadecimal characters'
      };
      return res.status(400).json(response);
    }

    const { masterKey } = value;
    
    // Set the new master key
    setMasterKey(masterKey);

    const response: ApiResponse = {
      success: true,
      data: { message: 'Master key updated successfully' }
    };

    res.json(response);
  } catch (error) {
    console.error('Error updating master key:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// GET /api/settings/llm-provider - Get current LLM provider configuration
router.get('/llm-provider', async (req: Request, res: Response) => {
  try {
    const currentProvider = (process.env.LLM_PROVIDER as ProviderType) || 'openai';
    const defaultModel = LLMProviderFactory.getDefaultModel();

    const response: ApiResponse = {
      success: true,
      data: {
        provider: currentProvider,
        defaultModel,
        availableProviders: ['openai', 'azure']
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Error getting LLM provider configuration:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

// POST /api/settings/llm-provider - Update LLM provider configuration
router.post('/llm-provider', async (req: Request, res: Response) => {
  try {
    const { error, value } = llmProviderSchema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: 'Invalid LLM provider configuration'
      };
      return res.status(400).json(response);
    }

    const { provider, defaultModel } = value;

    // Note: This endpoint returns success but doesn't actually update environment variables
    // In a real implementation, you would need to update a configuration file or database
    // and restart the application for changes to take effect

    const response: ApiResponse = {
      success: true,
      data: {
        message: 'LLM provider configuration updated. Restart required for changes to take effect.',
        provider,
        defaultModel
      }
    };

    res.json(response);
  } catch (error) {
    console.error('Error updating LLM provider configuration:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
    res.status(500).json(response);
  }
});

export { router as settingsRoutes };
